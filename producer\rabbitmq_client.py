"""
RabbitMQ客户端模块
用于发送市场数据到RabbitMQ队列
"""

import json
import logging
import time
from typing import Dict, Any, Optional
import yaml
import pika
from pika.exceptions import AMQPConnectionError, AMQPChannelError
import threading
from datetime import datetime


class RabbitMQClient:
    """RabbitMQ客户端类，用于发送消息到队列"""
    
    def __init__(self, config_path: str = "../config/rabbitmq_config.yaml"):
        """
        初始化RabbitMQ客户端
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.connection = None
        self.channel = None
        self.logger = self._setup_logger()
        self._lock = threading.Lock()
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # 创建文件处理器
        import os
        log_dir = os.path.dirname(self.config['logging']['file'])
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        handler = logging.FileHandler(self.config['logging']['file'])
        formatter = logging.Formatter(self.config['logging']['format'])
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        # 添加控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def connect(self) -> bool:
        """连接到RabbitMQ服务器"""
        try:
            rabbitmq_config = self.config['rabbitmq']
            
            # 创建连接参数
            credentials = pika.PlainCredentials(
                rabbitmq_config['username'],
                rabbitmq_config['password']
            )
            
            parameters = pika.ConnectionParameters(
                host=rabbitmq_config['host'],
                port=rabbitmq_config['port'],
                virtual_host=rabbitmq_config['virtual_host'],
                credentials=credentials,
                heartbeat=rabbitmq_config['connection']['heartbeat'],
                blocked_connection_timeout=rabbitmq_config['connection']['blocked_connection_timeout'],
                socket_timeout=rabbitmq_config['connection']['socket_timeout']
            )
            
            # 建立连接
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            # 声明交换机
            exchange_config = rabbitmq_config['exchange']
            self.channel.exchange_declare(
                exchange=exchange_config['name'],
                exchange_type=exchange_config['type'],
                durable=exchange_config['durable'],
                auto_delete=exchange_config['auto_delete']
            )
            
            # 声明队列
            queue_config = rabbitmq_config['queue']
            self.channel.queue_declare(
                queue=queue_config['name'],
                durable=queue_config['durable'],
                exclusive=queue_config['exclusive'],
                auto_delete=queue_config['auto_delete']
            )
            
            # 绑定队列到交换机
            self.channel.queue_bind(
                exchange=exchange_config['name'],
                queue=queue_config['name'],
                routing_key=rabbitmq_config['routing_key']
            )
            
            self.logger.info("成功连接到RabbitMQ服务器")
            return True
            
        except AMQPConnectionError as e:
            self.logger.error(f"连接RabbitMQ失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"连接过程中发生错误: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.connection and not self.connection.is_closed:
                self.connection.close()
                self.logger.info("已断开RabbitMQ连接")
        except Exception as e:
            self.logger.error(f"断开连接时发生错误: {e}")
    
    def send_message(self, data: Dict[str, Any]) -> bool:
        """
        发送消息到队列
        
        Args:
            data: 要发送的数据
            
        Returns:
            bool: 发送是否成功
        """
        with self._lock:
            try:
                # 检查连接状态
                if not self.connection or self.connection.is_closed:
                    if not self.connect():
                        return False
                
                # 准备消息数据
                message_data = {
                    'timestamp': datetime.now().isoformat(),
                    'data': data
                }
                
                # 序列化数据
                if self.config['app']['data_format'] == 'json':
                    message_body = json.dumps(message_data, ensure_ascii=False)
                else:
                    # 可以扩展支持其他格式如msgpack
                    message_body = json.dumps(message_data, ensure_ascii=False)
                
                # 发送消息
                rabbitmq_config = self.config['rabbitmq']
                self.channel.basic_publish(
                    exchange=rabbitmq_config['exchange']['name'],
                    routing_key=rabbitmq_config['routing_key'],
                    body=message_body,
                    properties=pika.BasicProperties(
                        delivery_mode=2,  # 消息持久化
                        content_type='application/json',
                        timestamp=int(time.time())
                    )
                )
                
                self.logger.debug(f"消息发送成功: {len(message_body)} bytes")
                return True
                
            except AMQPChannelError as e:
                self.logger.error(f"发送消息时通道错误: {e}")
                return False
            except Exception as e:
                self.logger.error(f"发送消息时发生错误: {e}")
                return False
    
    def send_batch_messages(self, data_list: list) -> int:
        """
        批量发送消息
        
        Args:
            data_list: 数据列表
            
        Returns:
            int: 成功发送的消息数量
        """
        success_count = 0
        for data in data_list:
            if self.send_message(data):
                success_count += 1
        
        self.logger.info(f"批量发送完成: {success_count}/{len(data_list)} 条消息成功")
        return success_count
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


# 全局RabbitMQ客户端实例
_rabbitmq_client = None


def get_rabbitmq_client(config_path: str = "../config/rabbitmq_config.yaml") -> RabbitMQClient:
    """获取RabbitMQ客户端单例"""
    global _rabbitmq_client
    if _rabbitmq_client is None:
        _rabbitmq_client = RabbitMQClient(config_path)
        _rabbitmq_client.connect()
    return _rabbitmq_client


def close_rabbitmq_client():
    """关闭RabbitMQ客户端"""
    global _rabbitmq_client
    if _rabbitmq_client:
        _rabbitmq_client.disconnect()
        _rabbitmq_client = None
