#!/usr/bin/env python3
"""
系统集成测试脚本
用于测试整个数据流：Producer -> RabbitMQ -> Consumer -> MySQL
"""

import json
import time
import requests
from rabbitmq_client import get_rabbitmq_client


def test_producer_to_rabbitmq():
    """测试生产者发送消息到RabbitMQ"""
    print("🧪 测试生产者发送消息到RabbitMQ...")
    
    # 模拟市场数据
    test_data = {
        '600000.SH': {
            'time': int(time.time() * 1000),
            'timetag': time.strftime('%Y%m%d %H:%M:%S'),
            'lastPrice': 12.74,
            'open': 12.78,
            'high': 12.84,
            'low': 12.64,
            'lastClose': 12.78,
            'amount': 557599600,
            'volume': 437681,
            'pvolume': 43768050,
            'stockStatus': 5,
            'openInt': 15,
            'settlementPrice': 0,
            'lastSettlementPrice': 12.78,
            'askPrice': [0, 0, 0, 0, 0],
            'bidPrice': [0, 0, 0, 0, 0],
            'askVol': [0, 0, 0, 0, 0],
            'bidVol': [0, 0, 0, 0, 0]
        }
    }
    
    try:
        # 获取RabbitMQ客户端
        client = get_rabbitmq_client()
        
        # 发送测试消息
        success = client.send_message(test_data)
        
        if success:
            print("✅ 消息发送成功")
            return True
        else:
            print("❌ 消息发送失败")
            return False
            
    except Exception as e:
        print(f"❌ 发送消息时发生错误: {e}")
        return False


def test_consumer_health():
    """测试消费者健康状态"""
    print("🧪 测试消费者健康状态...")
    
    try:
        response = requests.get('http://localhost:8080/health', timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ 消费者健康状态: {health_data['status']}")
            return True
        else:
            print(f"❌ 健康检查失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到消费者健康检查接口: {e}")
        return False


def test_prometheus_metrics():
    """测试Prometheus指标"""
    print("🧪 测试Prometheus指标...")
    
    try:
        response = requests.get('http://localhost:8000/metrics', timeout=5)
        
        if response.status_code == 200:
            metrics_text = response.text
            
            # 检查关键指标是否存在
            key_metrics = [
                'messages_received_total',
                'messages_processed_total',
                'database_operations_total',
                'system_uptime_seconds'
            ]
            
            missing_metrics = []
            for metric in key_metrics:
                if metric not in metrics_text:
                    missing_metrics.append(metric)
            
            if not missing_metrics:
                print("✅ Prometheus指标正常")
                return True
            else:
                print(f"❌ 缺少指标: {missing_metrics}")
                return False
        else:
            print(f"❌ 获取指标失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Prometheus指标接口: {e}")
        return False


def test_consumer_stats():
    """测试消费者统计信息"""
    print("🧪 测试消费者统计信息...")
    
    try:
        response = requests.get('http://localhost:8080/stats', timeout=5)
        
        if response.status_code == 200:
            stats_data = response.json()
            print(f"✅ 消费者统计信息获取成功")
            print(f"   - 接收消息数: {stats_data.get('consumer_stats', {}).get('messages_received', 0)}")
            print(f"   - 处理消息数: {stats_data.get('consumer_stats', {}).get('messages_processed', 0)}")
            print(f"   - 失败消息数: {stats_data.get('consumer_stats', {}).get('messages_failed', 0)}")
            return True
        else:
            print(f"❌ 获取统计信息失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到统计信息接口: {e}")
        return False


def test_end_to_end():
    """端到端测试"""
    print("🧪 开始端到端测试...")
    
    # 1. 检查消费者是否运行
    if not test_consumer_health():
        print("❌ 消费者服务未运行，请先启动消费者服务")
        return False
    
    # 2. 获取初始统计信息
    print("📊 获取初始统计信息...")
    initial_response = requests.get('http://localhost:8080/stats', timeout=5)
    initial_stats = initial_response.json() if initial_response.status_code == 200 else {}
    initial_processed = initial_stats.get('consumer_stats', {}).get('messages_processed', 0)
    
    # 3. 发送测试消息
    print("📤 发送测试消息...")
    if not test_producer_to_rabbitmq():
        return False
    
    # 4. 等待消息处理
    print("⏳ 等待消息处理...")
    time.sleep(5)
    
    # 5. 检查消息是否被处理
    print("📊 检查消息处理结果...")
    final_response = requests.get('http://localhost:8080/stats', timeout=5)
    final_stats = final_response.json() if final_response.status_code == 200 else {}
    final_processed = final_stats.get('consumer_stats', {}).get('messages_processed', 0)
    
    if final_processed > initial_processed:
        print(f"✅ 端到端测试成功！处理了 {final_processed - initial_processed} 条新消息")
        return True
    else:
        print("❌ 端到端测试失败，消息未被处理")
        return False


def main():
    """主测试函数"""
    print("🚀 开始系统集成测试")
    print("=" * 50)
    
    tests = [
        ("消费者健康检查", test_consumer_health),
        ("Prometheus指标", test_prometheus_metrics),
        ("消费者统计信息", test_consumer_stats),
        ("端到端测试", test_end_to_end)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统状态")
        return 1


if __name__ == '__main__':
    exit(main())
