from xtquant import xtdata
import time
import json
import pandas as pd

'''
stock_list = ["000001.SZ"]
period = '1m'
start_time ="20250101093000"
end_time ="20250401093000"

market_data = xtdata.get_market_data(stock_list=stock_list, period=period, start_time=start_time)

# The data comes as a dictionary with each field (time, open, high, etc.) as keys
# Each value is a DataFrame with stocks as rows and time periods as columns
# We need to reshape this into a long format DataFrame

# Create a list to store all records
records = []

# Get the time DataFrame to use as reference for time periods
time_df = market_data['time']

# Iterate through each stock
for stock in stock_list:
    # Iterate through each time period (columns)
    for time_col in time_df.columns:
        # Create a record for this stock at this time
        record = {
            'stock': stock,
            'time': market_data['time'].loc[stock, time_col],
            'open': market_data['open'].loc[stock, time_col],
            'high': market_data['high'].loc[stock, time_col],
            'low': market_data['low'].loc[stock, time_col],
            'close': market_data['close'].loc[stock, time_col],
            'volume': market_data['volume'].loc[stock, time_col],
            'amount': market_data['amount'].loc[stock, time_col],
            'settelementPrice': market_data['settelementPrice'].loc[stock, time_col],
            'openInterest': market_data['openInterest'].loc[stock, time_col],
            'preClose': market_data['preClose'].loc[stock, time_col],
            'suspendFlag': market_data['suspendFlag'].loc[stock, time_col]
        }
        records.append(record)

# Create DataFrame from records
df = pd.DataFrame(records)

# Convert time from timestamp to readable format
df['datetime'] = pd.to_datetime(df['time'], unit='ms')

# Remove rows with NaN values in essential columns (like close price)
df_clean = df.dropna(subset=['close'])

print(f"Total records: {len(df)}")
print(f"Records after removing NaN: {len(df_clean)}")
print("\nFirst few records:")
print(df_clean.head(10))
print("\nData info:")
print(df_clean.info())

print(df.head())
print(df.info())
'''
'''
stock_list = ["000001.SZ"]
market_data = xtdata.get_local_data(field_list=[], stock_list=stock_list, period='1d')
print(market_data)

'''

'''
list = xtdata.get_sector_list()

print(list)


stock_list = xtdata.get_stock_list_in_sector("证券龙头")
print(stock_list  )

# ['600030.SH', '600061.SH', '600109.SH', '600918.SH', '600958.SH', '600999.SH', '601059.SH', '601066.SH', '601108.SH', '601136.SH', '601211.SH', '601236.SH', '601377.SH', '601456.SH', '601555.SH', '601688.SH', '601788.SH', '601878.SH', '601881.SH', '601901.SH', '601990.SH', '601995.SH', '000166.SZ', '000776.SZ', '000783.SZ', '002673.SZ', '002736.SZ', '002939.SZ', '002945.SZ', '300059.SZ']

'''

import time

from xtquant import xtdata

code = ['600030.SH', '600061.SH', '600109.SH', '600918.SH', '600958.SH', '600999.SH', '601059.SH', '601066.SH', '601108.SH', '601136.SH', '601211.SH', '601236.SH', '601377.SH', '601456.SH', '601555.SH', '601688.SH', '601788.SH', '601878.SH', '601881.SH', '601901.SH', '601990.SH', '601995.SH', '000166.SZ', '000776.SZ', '000783.SZ', '002673.SZ', '002736.SZ', '002939.SZ', '002945.SZ', '300059.SZ']

 

#订阅最新行情
def callback_func(data):
    print('回调触发', data)

xtdata.subscribe_quote(code, period='1m', count=0, callback= callback_func)
 

#死循环 阻塞主线程退出
xtdata.run()