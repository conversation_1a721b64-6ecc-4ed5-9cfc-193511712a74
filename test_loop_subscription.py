#!/usr/bin/env python3
"""
测试循环订阅功能
验证producer是否正确循环订阅code列表中的每个股票
"""

import sys
import os

# 添加producer目录到Python路径
sys.path.append('producer')

def test_code_list():
    """测试股票代码列表"""
    print("🧪 测试股票代码列表...")
    
    try:
        # 导入collect_data模块来获取code列表
        import collect_data
        
        # 检查code变量是否存在
        if hasattr(collect_data, 'code'):
            code_list = collect_data.code
            print(f"✅ 找到股票代码列表，共 {len(code_list)} 只股票")
            
            # 显示前10个股票代码
            print("   前10个股票代码:")
            for i, stock_code in enumerate(code_list[:10], 1):
                print(f"     {i:2d}. {stock_code}")
            
            if len(code_list) > 10:
                print(f"     ... 还有 {len(code_list) - 10} 只股票")
            
            # 验证股票代码格式
            valid_codes = 0
            for stock_code in code_list:
                if isinstance(stock_code, str) and ('.' in stock_code):
                    valid_codes += 1
            
            print(f"   有效格式的股票代码: {valid_codes}/{len(code_list)}")
            
            return True
        else:
            print("❌ 未找到code变量")
            return False
            
    except ImportError as e:
        print(f"❌ 导入collect_data模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_callback_function():
    """测试回调函数"""
    print("\n🧪 测试回调函数...")
    
    try:
        import collect_data
        
        # 检查callback_func是否存在
        if hasattr(collect_data, 'callback_func'):
            print("✅ 找到callback_func函数")
            
            # 模拟测试数据
            test_data = {
                '600000.SH': {
                    'time': 1750316403000,
                    'timetag': '20250619 15:00:03',
                    'lastPrice': 12.74,
                    'open': 12.78
                },
                '000001.SZ': {
                    'time': 1750316403000,
                    'timetag': '20250619 15:00:03',
                    'lastPrice': 25.30,
                    'open': 25.20
                }
            }
            
            print("   测试数据格式:")
            print(f"     股票数量: {len(test_data)}")
            print(f"     股票代码: {list(test_data.keys())}")
            
            # 注意：这里不实际调用callback_func，因为它需要RabbitMQ连接
            print("✅ 回调函数结构验证通过")
            
            return True
        else:
            print("❌ 未找到callback_func函数")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_subscription_logic():
    """测试订阅逻辑"""
    print("\n🧪 测试订阅逻辑...")
    
    try:
        # 读取collect_data.py文件内容
        with open('producer/collect_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含循环订阅逻辑
        required_patterns = [
            'for stock_code in code:',
            'xtdata.subscribe_quote(stock_code',
            'success_count',
            'logger.info(f"成功订阅股票: {stock_code}")',
            'record_error("stock_subscription_failed")'
        ]
        
        missing_patterns = []
        found_patterns = []
        
        for pattern in required_patterns:
            if pattern in content:
                found_patterns.append(pattern)
            else:
                missing_patterns.append(pattern)
        
        print(f"   找到的模式: {len(found_patterns)}/{len(required_patterns)}")
        
        for pattern in found_patterns:
            print(f"     ✅ {pattern}")
        
        if missing_patterns:
            print("   缺少的模式:")
            for pattern in missing_patterns:
                print(f"     ❌ {pattern}")
            return False
        
        print("✅ 订阅逻辑验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理...")
    
    try:
        with open('producer/collect_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查错误处理相关代码
        error_handling_patterns = [
            'try:',
            'except Exception as e:',
            'logger.error(',
            'record_error(',
            'time.sleep(0.1)'  # 订阅延迟
        ]
        
        found_count = 0
        for pattern in error_handling_patterns:
            if pattern in content:
                found_count += 1
                print(f"     ✅ {pattern}")
        
        print(f"   错误处理模式: {found_count}/{len(error_handling_patterns)}")
        
        if found_count >= len(error_handling_patterns) - 1:  # 允许一个模式缺失
            print("✅ 错误处理验证通过")
            return True
        else:
            print("❌ 错误处理不完整")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_monitoring_integration():
    """测试监控集成"""
    print("\n🧪 测试监控集成...")
    
    try:
        with open('producer/collect_data.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查监控相关代码
        monitoring_patterns = [
            'set_subscribed_stocks_count(success_count)',
            'set_qmt_connection_status(',
            'record_error("stock_subscription_failed")',
            'record_data_callback()',
            'record_message_sent('
        ]
        
        found_count = 0
        for pattern in monitoring_patterns:
            if pattern in content:
                found_count += 1
                print(f"     ✅ {pattern}")
            else:
                print(f"     ❌ {pattern}")
        
        print(f"   监控集成模式: {found_count}/{len(monitoring_patterns)}")
        
        if found_count >= 4:  # 至少要有4个监控模式
            print("✅ 监控集成验证通过")
            return True
        else:
            print("❌ 监控集成不完整")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始循环订阅功能测试")
    print("=" * 50)
    
    tests = [
        ("股票代码列表", test_code_list),
        ("回调函数", test_callback_function),
        ("订阅逻辑", test_subscription_logic),
        ("错误处理", test_error_handling),
        ("监控集成", test_monitoring_integration)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 循环订阅功能测试全部通过！")
        print("\n📋 功能说明:")
        print("   ✅ 循环遍历code列表中的每个股票代码")
        print("   ✅ 为每个股票单独调用xtdata.subscribe_quote()")
        print("   ✅ 记录订阅成功和失败的数量")
        print("   ✅ 集成监控指标记录")
        print("   ✅ 完善的错误处理机制")
        print("\n🚀 可以启动服务测试:")
        print("   cd producer && python collect_data.py")
        return 0
    else:
        print("⚠️  部分测试失败，请检查代码修改")
        return 1


if __name__ == '__main__':
    exit(main())
