#!/usr/bin/env python3
"""
修复日志编码问题
将现有的GBK编码日志文件转换为UTF-8编码
"""

import os
import shutil
import logging
from datetime import datetime


def backup_existing_log():
    """备份现有日志文件"""
    log_file = '../logs/producer.log'
    
    if not os.path.exists(log_file):
        print("📝 日志文件不存在，无需备份")
        return True
    
    try:
        # 创建备份文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f'../logs/producer_backup_{timestamp}.log'
        
        # 备份文件
        shutil.copy2(log_file, backup_file)
        print(f"✅ 已备份日志文件到: {backup_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 备份日志文件失败: {e}")
        return False


def convert_log_encoding():
    """转换日志文件编码"""
    log_file = '../logs/producer.log'
    
    if not os.path.exists(log_file):
        print("📝 日志文件不存在，无需转换")
        return True
    
    try:
        # 尝试用GBK读取现有文件
        print("🔄 正在转换日志文件编码...")
        
        with open(log_file, 'r', encoding='gbk') as f:
            content = f.read()
        
        print(f"   原文件大小: {len(content)} 字符")
        
        # 用UTF-8重新写入
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ 日志文件编码转换完成 (GBK -> UTF-8)")
        
        # 验证转换结果
        with open(log_file, 'r', encoding='utf-8') as f:
            test_content = f.read()
        
        if len(test_content) == len(content):
            print("✅ 编码转换验证通过")
            return True
        else:
            print("❌ 编码转换验证失败")
            return False
        
    except UnicodeDecodeError as e:
        print(f"❌ GBK解码失败: {e}")
        print("🔄 尝试删除并重新创建日志文件...")
        return recreate_log_file()
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")
        return False


def recreate_log_file():
    """重新创建日志文件"""
    log_file = '../logs/producer.log'
    
    try:
        # 删除现有文件
        if os.path.exists(log_file):
            os.remove(log_file)
            print("🗑️  已删除现有日志文件")
        
        # 创建新的UTF-8编码日志文件
        with open(log_file, 'w', encoding='utf-8') as f:
            f.write(f"# Producer日志文件 (UTF-8编码)\n")
            f.write(f"# 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 编码: UTF-8\n\n")
        
        print("✅ 已创建新的UTF-8编码日志文件")
        return True
        
    except Exception as e:
        print(f"❌ 重新创建日志文件失败: {e}")
        return False


def test_utf8_logging():
    """测试UTF-8日志记录"""
    print("\n🧪 测试UTF-8日志记录...")
    
    try:
        # 配置日志
        log_file = '../logs/producer.log'
        
        # 创建logger
        logger = logging.getLogger('encoding_test')
        logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 添加UTF-8文件处理器
        file_handler = logging.FileHandler(log_file, mode='a', encoding='utf-8')
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 测试中文日志
        test_messages = [
            "🔧 日志编码修复测试开始",
            "📊 股票代码: 600000.SH (浦发银行)",
            "✅ 订阅成功: 中信证券",
            "⚠️ 警告: 网络连接不稳定",
            "❌ 错误: 数据解析失败",
            "🎉 修复完成: 日志编码问题已解决"
        ]
        
        for message in test_messages:
            logger.info(message)
        
        # 移除处理器
        logger.removeHandler(file_handler)
        file_handler.close()
        
        print("✅ UTF-8日志记录测试完成")
        
        # 验证文件内容
        with open(log_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含测试消息
        if "日志编码修复测试开始" in content:
            print("✅ UTF-8编码验证通过")
            return True
        else:
            print("❌ UTF-8编码验证失败")
            return False
        
    except Exception as e:
        print(f"❌ UTF-8日志测试失败: {e}")
        return False


def show_log_info():
    """显示日志文件信息"""
    print("\n📋 日志文件信息:")
    
    log_file = '../logs/producer.log'
    
    if not os.path.exists(log_file):
        print("   日志文件不存在")
        return
    
    try:
        # 文件大小
        size = os.path.getsize(log_file)
        print(f"   文件大小: {size} 字节")
        
        # 文件路径
        abs_path = os.path.abspath(log_file)
        print(f"   文件路径: {abs_path}")
        
        # 读取最后几行
        with open(log_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"   总行数: {len(lines)}")
        
        if lines:
            print("   最后3行内容:")
            for line in lines[-3:]:
                if line.strip():
                    print(f"     {line.strip()}")
        
    except Exception as e:
        print(f"   读取失败: {e}")


def main():
    """主修复函数"""
    print("🚀 开始修复日志编码问题")
    print("=" * 50)
    
    steps = [
        ("备份现有日志", backup_existing_log),
        ("转换日志编码", convert_log_encoding),
        ("测试UTF-8日志", test_utf8_logging)
    ]
    
    success_count = 0
    
    for step_name, step_func in steps:
        print(f"\n🔧 {step_name}...")
        print("-" * 30)
        
        try:
            if step_func():
                success_count += 1
                print(f"✅ {step_name} 完成")
            else:
                print(f"❌ {step_name} 失败")
        except Exception as e:
            print(f"❌ {step_name} 异常: {e}")
    
    # 显示日志信息
    show_log_info()
    
    print("\n" + "=" * 50)
    print(f"📊 修复结果: {success_count}/{len(steps)} 步骤成功")
    
    if success_count == len(steps):
        print("🎉 日志编码问题修复完成！")
        print("\n📋 修复内容:")
        print("   ✅ 备份了原有日志文件")
        print("   ✅ 转换编码为UTF-8")
        print("   ✅ 验证UTF-8日志记录")
        print("\n🚀 现在可以正常查看中文日志了")
        return 0
    else:
        print("⚠️  部分修复步骤失败")
        return 1


if __name__ == '__main__':
    exit(main())
