#!/usr/bin/env python3
"""
Producer服务测试运行脚本
"""

import subprocess
import sys
import os
from pathlib import Path


def run_tests():
    """运行所有测试"""
    print("开始运行Producer服务测试...")
    
    # 确保在正确的目录
    test_dir = Path(__file__).parent
    os.chdir(test_dir)
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'test_producer.py', 
            '-v',  # 详细输出
            '--tb=short',  # 简短的traceback
            '--cov=.',  # 代码覆盖率
            '--cov-report=term-missing',  # 显示未覆盖的行
            '--cov-report=html:htmlcov',  # HTML覆盖率报告
            '--disable-warnings'  # 禁用警告
        ], capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ 所有测试通过!")
            print("📊 HTML覆盖率报告已生成: htmlcov/index.html")
        else:
            print(f"\n❌ 测试失败，退出码: {result.returncode}")
            
        return result.returncode
        
    except FileNotFoundError:
        print("❌ pytest未安装，请运行: pip install pytest pytest-cov")
        return 1
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")
        return 1


def install_test_dependencies():
    """安装测试依赖"""
    print("安装测试依赖...")
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'pytest', 'pytest-cov', 'pytest-mock', 'psutil'
        ], check=True)
        print("✅ 测试依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装测试依赖失败: {e}")
        return False


def run_specific_test(test_name):
    """运行特定测试"""
    print(f"运行特定测试: {test_name}")
    
    test_dir = Path(__file__).parent
    os.chdir(test_dir)
    
    try:
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            f'test_producer.py::{test_name}',
            '-v',
            '--tb=short'
        ], capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        return result.returncode
        
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")
        return 1


def main():
    """主函数"""
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == '--install':
            if install_test_dependencies():
                sys.exit(run_tests())
            else:
                sys.exit(1)
        elif command == '--test':
            if len(sys.argv) > 2:
                test_name = sys.argv[2]
                sys.exit(run_specific_test(test_name))
            else:
                print("请指定测试名称，例如: python run_tests.py --test TestProducerMetricsCollector")
                sys.exit(1)
        elif command == '--help':
            print("使用方法:")
            print("  python run_tests.py              # 运行所有测试")
            print("  python run_tests.py --install    # 安装依赖并运行测试")
            print("  python run_tests.py --test <name> # 运行特定测试")
            print("  python run_tests.py --help       # 显示帮助")
            sys.exit(0)
        else:
            print(f"未知命令: {command}")
            sys.exit(1)
    else:
        sys.exit(run_tests())


if __name__ == '__main__':
    main()
