"""
RabbitMQ消费者模块
用于消费市场数据消息并存储到数据库
"""

import json
import logging
import time
import uuid
from typing import Dict, Any, Callable
import yaml
import pika
from pika.exceptions import AMQPConnectionError, AMQPChannelError
import threading
from datetime import datetime

from database_manager import get_database_manager


class RabbitMQConsumer:
    """RabbitMQ消费者类"""
    
    def __init__(self, config_path: str = "config.yaml", db_config_path: str = "config/database.yaml"):
        """
        初始化RabbitMQ消费者
        
        Args:
            config_path: RabbitMQ配置文件路径
            db_config_path: 数据库配置文件路径
        """
        self.config = self._load_config(config_path)
        self.db_manager = get_database_manager(db_config_path)
        self.logger = self._setup_logger()
        
        self.connection = None
        self.channel = None
        self.consuming = False
        self._consumer_tag = None
        
        # 统计信息
        self.stats = {
            'messages_received': 0,
            'messages_processed': 0,
            'messages_failed': 0,
            'start_time': None
        }
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            # 文件处理器
            file_handler = logging.FileHandler('logs/consumer.log')
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.addHandler(file_handler)
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(file_formatter)
            logger.addHandler(console_handler)
        
        return logger
    
    def connect(self) -> bool:
        """连接到RabbitMQ服务器"""
        try:
            rabbitmq_config = self.config['rabbitmq']
            
            # 创建连接参数
            credentials = pika.PlainCredentials(
                rabbitmq_config['username'],
                rabbitmq_config['password']
            )
            
            parameters = pika.ConnectionParameters(
                host=rabbitmq_config['host'],
                port=rabbitmq_config['port'],
                virtual_host=rabbitmq_config['virtual_host'],
                credentials=credentials,
                heartbeat=rabbitmq_config['connection']['heartbeat'],
                blocked_connection_timeout=rabbitmq_config['connection']['blocked_connection_timeout'],
                socket_timeout=rabbitmq_config['connection']['socket_timeout']
            )
            
            # 建立连接
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            # 设置QoS
            self.channel.basic_qos(prefetch_count=10)
            
            self.logger.info("成功连接到RabbitMQ服务器")
            return True
            
        except AMQPConnectionError as e:
            self.logger.error(f"连接RabbitMQ失败: {e}")
            return False
        except Exception as e:
            self.logger.error(f"连接过程中发生错误: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.consuming:
                self.stop_consuming()
            
            if self.connection and not self.connection.is_closed:
                self.connection.close()
                self.logger.info("已断开RabbitMQ连接")
        except Exception as e:
            self.logger.error(f"断开连接时发生错误: {e}")
    
    def _process_message(self, channel, method, properties, body) -> bool:
        """
        处理接收到的消息
        
        Args:
            channel: 通道
            method: 方法
            properties: 属性
            body: 消息体
            
        Returns:
            bool: 处理是否成功
        """
        message_id = str(uuid.uuid4())
        start_time = time.time()
        
        try:
            self.stats['messages_received'] += 1
            
            # 解析消息
            message_data = json.loads(body.decode('utf-8'))
            self.logger.debug(f"接收到消息: {message_id}")
            
            # 提取数据
            if 'data' in message_data:
                market_data = message_data['data']
            else:
                market_data = message_data
            
            # 处理每个股票的数据
            success_count = 0
            total_count = 0

            for symbol, quote_data_list in market_data.items():
                # 新数据格式：每个股票对应一个数组
                if not isinstance(quote_data_list, list):
                    self.logger.warning(f"股票 {symbol} 的数据格式不正确，期望数组格式")
                    continue

                # 处理数组中的每个行情数据
                for quote_data in quote_data_list:
                    total_count += 1

                    try:
                        # 插入数据库
                        if self.db_manager.insert_market_quote(symbol, quote_data):
                            success_count += 1
                            self.logger.debug(f"成功处理 {symbol} 的数据 (时间: {quote_data.get('time', 'N/A')})")
                        else:
                            self.logger.warning(f"处理 {symbol} 的数据失败")

                    except Exception as e:
                        self.logger.error(f"处理 {symbol} 数据时发生错误: {e}")
            
            # 记录处理结果
            if success_count == total_count:
                status = 'SUCCESS'
                self.stats['messages_processed'] += 1
            else:
                status = 'PARTIAL_SUCCESS'
                self.stats['messages_failed'] += 1

            # 统计股票数量
            symbol_count = len(market_data)

            # 记录处理日志
            self.db_manager.log_processing_result(
                message_id=message_id,
                symbol=f"BATCH_{symbol_count}_SYMBOLS_{total_count}_RECORDS",
                status=status,
                error_message=None if success_count == total_count else f"部分失败: {success_count}/{total_count}",
                raw_data=market_data
            )

            processing_time = time.time() - start_time
            self.logger.info(f"消息处理完成: {message_id}, 股票数: {symbol_count}, 记录数: {success_count}/{total_count}, 耗时: {processing_time:.3f}s")
            
            # 确认消息
            channel.basic_ack(delivery_tag=method.delivery_tag)
            return True
            
        except json.JSONDecodeError as e:
            self.logger.error(f"消息格式错误 {message_id}: {e}")
            self.stats['messages_failed'] += 1
            
            # 记录错误
            self.db_manager.log_processing_result(
                message_id=message_id,
                symbol="UNKNOWN",
                status='FAILED',
                error_message=f"JSON解析错误: {e}",
                raw_data={'raw_body': body.decode('utf-8', errors='ignore')}
            )
            
            # 拒绝消息（不重新入队）
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=False)
            return False
            
        except Exception as e:
            self.logger.error(f"处理消息时发生未知错误 {message_id}: {e}")
            self.stats['messages_failed'] += 1
            
            # 记录错误
            self.db_manager.log_processing_result(
                message_id=message_id,
                symbol="UNKNOWN",
                status='FAILED',
                error_message=str(e),
                raw_data={'raw_body': body.decode('utf-8', errors='ignore')}
            )
            
            # 拒绝消息并重新入队（可能是临时错误）
            channel.basic_nack(delivery_tag=method.delivery_tag, requeue=True)
            return False
    
    def start_consuming(self):
        """开始消费消息"""
        if not self.connection or self.connection.is_closed:
            if not self.connect():
                raise Exception("无法连接到RabbitMQ服务器")
        
        try:
            queue_name = self.config['rabbitmq']['queue']['name']
            
            # 设置消费者
            self._consumer_tag = self.channel.basic_consume(
                queue=queue_name,
                on_message_callback=self._process_message
            )
            
            self.consuming = True
            self.stats['start_time'] = datetime.now()
            
            self.logger.info(f"开始消费队列: {queue_name}")
            self.logger.info("等待消息... 按 CTRL+C 停止")
            
            # 开始消费
            self.channel.start_consuming()
            
        except KeyboardInterrupt:
            self.logger.info("接收到中断信号，正在停止消费...")
            self.stop_consuming()
        except Exception as e:
            self.logger.error(f"消费过程中发生错误: {e}")
            raise
    
    def stop_consuming(self):
        """停止消费消息"""
        if self.consuming and self.channel:
            try:
                self.channel.stop_consuming()
                self.consuming = False
                self.logger.info("已停止消费消息")
            except Exception as e:
                self.logger.error(f"停止消费时发生错误: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        
        if stats['start_time']:
            runtime = datetime.now() - stats['start_time']
            stats['runtime_seconds'] = runtime.total_seconds()
            
            if stats['runtime_seconds'] > 0:
                stats['messages_per_second'] = stats['messages_received'] / stats['runtime_seconds']
            else:
                stats['messages_per_second'] = 0
        
        return stats
    
    def health_check(self) -> Dict[str, Any]:
        """健康检查"""
        try:
            # 检查RabbitMQ连接
            rabbitmq_status = 'healthy' if (self.connection and not self.connection.is_closed) else 'unhealthy'
            
            # 检查数据库连接
            db_health = self.db_manager.health_check()
            
            # 检查消费状态
            consumer_status = 'consuming' if self.consuming else 'stopped'
            
            return {
                'status': 'healthy' if rabbitmq_status == 'healthy' and db_health['status'] == 'healthy' else 'unhealthy',
                'rabbitmq': rabbitmq_status,
                'database': db_health,
                'consumer': consumer_status,
                'stats': self.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def __enter__(self):
        """上下文管理器入口"""
        self.connect()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.disconnect()


# 全局消费者实例
_consumer = None


def get_consumer(config_path: str = "config.yaml", db_config_path: str = "config/database.yaml") -> RabbitMQConsumer:
    """获取消费者单例"""
    global _consumer
    if _consumer is None:
        _consumer = RabbitMQConsumer(config_path, db_config_path)
    return _consumer


def close_consumer():
    """关闭消费者"""
    global _consumer
    if _consumer:
        _consumer.disconnect()
        _consumer = None
