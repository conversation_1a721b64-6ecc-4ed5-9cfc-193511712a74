"""
RabbitMQ客户端单元测试
"""

import pytest
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock
import yaml
from rabbitmq_client import Rabbit<PERSON>Q<PERSON>lient, get_rabbitmq_client, close_rabbitmq_client


class TestRabbitMQClient:
    """RabbitMQ客户端测试类"""
    
    @pytest.fixture
    def config_data(self):
        """测试配置数据"""
        return {
            'rabbitmq': {
                'host': 'localhost',
                'port': 5672,
                'username': 'test_user',
                'password': 'test_pass',
                'virtual_host': '/',
                'queue': {
                    'name': 'test_queue',
                    'durable': True,
                    'auto_delete': False,
                    'exclusive': False
                },
                'exchange': {
                    'name': 'test_exchange',
                    'type': 'direct',
                    'durable': True,
                    'auto_delete': False
                },
                'routing_key': 'test.key',
                'connection': {
                    'heartbeat': 600,
                    'blocked_connection_timeout': 300,
                    'socket_timeout': 10,
                    'retry_delay': 5,
                    'max_retries': 3
                }
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file': 'test.log'
            },
            'app': {
                'batch_size': 100,
                'flush_interval': 5,
                'data_format': 'json',
                'compression': False
            }
        }
    
    @pytest.fixture
    def config_file(self, config_data):
        """创建临时配置文件"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(config_data, f)
            config_path = f.name
        
        yield config_path
        
        # 清理
        if os.path.exists(config_path):
            os.unlink(config_path)
    
    @pytest.fixture
    def rabbitmq_client(self, config_file):
        """创建RabbitMQ客户端实例"""
        return RabbitMQClient(config_file)
    
    def test_load_config_success(self, config_file, config_data):
        """测试成功加载配置文件"""
        client = RabbitMQClient(config_file)
        assert client.config == config_data
    
    def test_load_config_file_not_found(self):
        """测试配置文件不存在的情况"""
        with pytest.raises(FileNotFoundError):
            RabbitMQClient("nonexistent.yaml")
    
    def test_load_config_invalid_yaml(self):
        """测试无效YAML格式"""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("invalid: yaml: content: [")
            invalid_config_path = f.name
        
        try:
            with pytest.raises(ValueError):
                RabbitMQClient(invalid_config_path)
        finally:
            os.unlink(invalid_config_path)
    
    @patch('rabbitmq_client.pika.BlockingConnection')
    def test_connect_success(self, mock_connection, rabbitmq_client):
        """测试成功连接RabbitMQ"""
        # 模拟连接和通道
        mock_conn = Mock()
        mock_channel = Mock()
        mock_connection.return_value = mock_conn
        mock_conn.channel.return_value = mock_channel
        
        # 执行连接
        result = rabbitmq_client.connect()
        
        # 验证结果
        assert result is True
        assert rabbitmq_client.connection == mock_conn
        assert rabbitmq_client.channel == mock_channel
        
        # 验证调用
        mock_channel.exchange_declare.assert_called_once()
        mock_channel.queue_declare.assert_called_once()
        mock_channel.queue_bind.assert_called_once()
    
    @patch('rabbitmq_client.pika.BlockingConnection')
    def test_connect_failure(self, mock_connection, rabbitmq_client):
        """测试连接失败"""
        from pika.exceptions import AMQPConnectionError
        mock_connection.side_effect = AMQPConnectionError("Connection failed")
        
        result = rabbitmq_client.connect()
        
        assert result is False
        assert rabbitmq_client.connection is None
        assert rabbitmq_client.channel is None
    
    def test_disconnect(self, rabbitmq_client):
        """测试断开连接"""
        # 模拟连接
        mock_connection = Mock()
        mock_connection.is_closed = False
        rabbitmq_client.connection = mock_connection
        
        rabbitmq_client.disconnect()
        
        mock_connection.close.assert_called_once()
    
    @patch('rabbitmq_client.pika.BlockingConnection')
    def test_send_message_success(self, mock_connection, rabbitmq_client):
        """测试成功发送消息"""
        # 设置模拟
        mock_conn = Mock()
        mock_channel = Mock()
        mock_connection.return_value = mock_conn
        mock_conn.channel.return_value = mock_channel
        mock_conn.is_closed = False
        
        # 连接
        rabbitmq_client.connect()
        
        # 测试数据
        test_data = {'symbol': 'AAPL', 'price': 150.0}
        
        # 发送消息
        result = rabbitmq_client.send_message(test_data)
        
        # 验证结果
        assert result is True
        mock_channel.basic_publish.assert_called_once()
        
        # 验证发布的参数
        call_args = mock_channel.basic_publish.call_args
        assert call_args[1]['exchange'] == 'test_exchange'
        assert call_args[1]['routing_key'] == 'test.key'
        
        # 验证消息体包含测试数据
        message_body = call_args[1]['body']
        message_data = json.loads(message_body)
        assert message_data['data'] == test_data
        assert 'timestamp' in message_data
    
    @patch('rabbitmq_client.pika.BlockingConnection')
    def test_send_message_connection_closed(self, mock_connection, rabbitmq_client):
        """测试连接关闭时自动重连"""
        # 第一次连接
        mock_conn1 = Mock()
        mock_channel1 = Mock()
        mock_conn1.is_closed = True  # 连接已关闭
        
        # 第二次连接（重连）
        mock_conn2 = Mock()
        mock_channel2 = Mock()
        mock_conn2.is_closed = False
        mock_conn2.channel.return_value = mock_channel2
        
        mock_connection.side_effect = [mock_conn1, mock_conn2]
        
        # 设置初始连接
        rabbitmq_client.connection = mock_conn1
        rabbitmq_client.channel = mock_channel1
        
        # 发送消息
        test_data = {'test': 'data'}
        result = rabbitmq_client.send_message(test_data)
        
        # 验证重连和发送
        assert result is True
        assert mock_connection.call_count == 2  # 重连了一次
    
    def test_send_batch_messages(self, rabbitmq_client):
        """测试批量发送消息"""
        # 模拟send_message方法
        with patch.object(rabbitmq_client, 'send_message') as mock_send:
            mock_send.return_value = True
            
            test_data_list = [
                {'symbol': 'AAPL', 'price': 150.0},
                {'symbol': 'GOOGL', 'price': 2500.0},
                {'symbol': 'MSFT', 'price': 300.0}
            ]
            
            result = rabbitmq_client.send_batch_messages(test_data_list)
            
            assert result == 3
            assert mock_send.call_count == 3
    
    def test_context_manager(self, config_file):
        """测试上下文管理器"""
        with patch('rabbitmq_client.pika.BlockingConnection') as mock_connection:
            mock_conn = Mock()
            mock_channel = Mock()
            mock_connection.return_value = mock_conn
            mock_conn.channel.return_value = mock_channel
            mock_conn.is_closed = False
            
            with RabbitMQClient(config_file) as client:
                assert client.connection == mock_conn
                assert client.channel == mock_channel
            
            # 验证连接被关闭
            mock_conn.close.assert_called_once()


class TestGlobalFunctions:
    """测试全局函数"""
    
    def test_get_rabbitmq_client_singleton(self, config_file):
        """测试单例模式"""
        with patch('rabbitmq_client.pika.BlockingConnection'):
            client1 = get_rabbitmq_client(config_file)
            client2 = get_rabbitmq_client(config_file)
            
            assert client1 is client2
    
    def test_close_rabbitmq_client(self, config_file):
        """测试关闭全局客户端"""
        with patch('rabbitmq_client.pika.BlockingConnection'):
            client = get_rabbitmq_client(config_file)
            
            with patch.object(client, 'disconnect') as mock_disconnect:
                close_rabbitmq_client()
                mock_disconnect.assert_called_once()


class TestIntegration:
    """集成测试"""
    
    @pytest.fixture
    def mock_callback_data(self):
        """模拟回调数据"""
        return {
            'symbol': '000001.SZ',
            'time': 1640995200000,
            'open': 11.73,
            'high': 11.77,
            'low': 11.70,
            'close': 11.75,
            'volume': 1000000,
            'amount': 11750000.0
        }
    
    @patch('test.get_rabbitmq_client')
    def test_callback_func_integration(self, mock_get_client, mock_callback_data):
        """测试callback_func集成"""
        # 模拟RabbitMQ客户端
        mock_client = Mock()
        mock_client.send_message.return_value = True
        mock_get_client.return_value = mock_client
        
        # 导入并测试callback_func
        import test
        test.callback_func(mock_callback_data)
        
        # 验证消息被发送
        mock_client.send_message.assert_called_once_with(mock_callback_data)


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
