# 市场数据生产者服务 (Producer)

这是一个集成了Prometheus监控和健康检查功能的市场数据生产者服务，用于从QMT获取股票行情数据并发送到RabbitMQ队列。

## 功能特性

- ✅ **QMT数据订阅**: 实时订阅股票行情数据
- ✅ **RabbitMQ消息发送**: 将数据发送到消息队列
- ✅ **Prometheus监控**: 暴露详细的服务和系统指标
- ✅ **健康检查**: 提供HTTP接口用于服务健康状态检测
- ✅ **系统监控**: 监控CPU、内存、磁盘、网络等系统资源
- ✅ **错误处理**: 完善的错误处理和重试机制
- ✅ **优雅关闭**: 支持优雅关闭和资源清理

## 项目结构

```
producer/
├── collect_data.py         # 主服务程序
├── rabbitmq_client.py      # RabbitMQ客户端
├── test_producer.py        # 单元测试
├── run_tests.py           # 测试运行脚本
└── README.md              # 使用文档

# 监控模块 (在项目根目录)
├── producer_metrics.py     # Prometheus指标收集
└── producer_health.py      # 健康检查服务
```

## 安装依赖

```bash
pip install -r requirements.txt
```

确保安装了以下额外依赖：
```bash
pip install psutil prometheus-client
```

## 配置说明

### RabbitMQ配置 (config/rabbitmq_config.yaml)

```yaml
rabbitmq:
  host: "localhost"
  port: 5672
  username: "guest"
  password: "guest"
  virtual_host: "/"

  queue:
    name: "market_data_queue"
    durable: true

  exchange:
    name: "market_data_exchange"
    type: "direct"

  routing_key: "market.data"

  connection:
    heartbeat: 600
    blocked_connection_timeout: 300
    socket_timeout: 10

app:
  data_format: "json"
  batch_size: 50
  flush_interval: 2
```

**注意**: 配置文件位于项目根目录的 `config/rabbitmq_config.yaml`

## 使用方法

### 1. 启动服务

```bash
cd producer
python collect_data.py
```

### 2. 服务端点

启动后，服务将提供以下端点：

- **健康检查**: `http://localhost:8081/health`
- **详细健康检查**: `http://localhost:8081/health/detailed`
- **存活性检查**: `http://localhost:8081/health/live`
- **就绪性检查**: `http://localhost:8081/health/ready`
- **系统信息**: `http://localhost:8081/system`
- **统计信息**: `http://localhost:8081/stats`
- **Prometheus指标**: `http://localhost:8001/metrics`

### 3. 监控股票列表

默认监控的股票代码：
```python
code = [
    '600030.SH', '600061.SH', '600109.SH', '600918.SH', '600958.SH',
    '600999.SH', '601059.SH', '601066.SH', '601108.SH', '601136.SH',
    # ... 更多股票代码
]
```

可以在 `collect_data.py` 中修改 `code` 列表来调整监控的股票。

## 监控指标

### 服务状态指标

- `producer_service_uptime_seconds`: 服务运行时间
- `producer_service_status`: 服务状态 (1=运行, 0=停止)
- `producer_qmt_connection_status`: QMT连接状态
- `producer_rabbitmq_connection_status`: RabbitMQ连接状态

### 业务指标

- `producer_messages_sent_total`: 发送的消息总数
- `producer_message_send_duration_seconds`: 消息发送耗时
- `producer_data_callbacks_total`: 数据回调总数
- `producer_subscribed_stocks_count`: 订阅的股票数量

### 系统资源指标

- `producer_cpu_usage_percent`: CPU使用率
- `producer_memory_usage_bytes`: 内存使用量
- `producer_memory_usage_percent`: 内存使用率
- `producer_disk_usage_percent`: 磁盘使用率
- `producer_network_bytes_sent_total`: 网络发送字节数
- `producer_network_bytes_recv_total`: 网络接收字节数

### 错误指标

- `producer_errors_total`: 错误总数 (按错误类型分类)

## 健康检查

### 基本健康检查

```bash
curl http://localhost:8081/health
```

返回示例：
```json
{
  "status": "healthy",
  "timestamp": "2025-06-19T15:00:03",
  "version": "1.0.0",
  "service": "market_data_producer"
}
```

### 详细健康检查

```bash
curl http://localhost:8081/health/detailed
```

返回详细的系统状态信息，包括CPU、内存、磁盘使用情况。

### 系统信息

```bash
curl http://localhost:8081/system
```

返回系统平台信息、Python版本、CPU核心数等。

## 测试

### 运行所有测试

```bash
cd producer
python run_tests.py
```

### 安装依赖并运行测试

```bash
python run_tests.py --install
```

### 运行特定测试

```bash
python run_tests.py --test TestProducerMetricsCollector
```

## 日志

服务日志保存在 `../logs/producer.log`，包含：
- 服务启动和关闭信息
- 数据回调和发送状态
- 错误和异常信息
- 监控状态变化

## 性能优化

### 1. 系统资源监控

- 监控CPU使用率，避免超过90%
- 监控内存使用率，避免超过90%
- 监控磁盘空间，避免满盘

### 2. 消息发送优化

- 监控消息发送耗时
- 检查RabbitMQ连接状态
- 实现重试机制

### 3. 数据处理优化

- 监控回调函数执行时间
- 优化数据序列化过程
- 减少不必要的日志输出

## 故障排除

### 常见问题

1. **QMT连接失败**
   - 检查QMT服务是否运行
   - 验证股票代码是否正确
   - 查看 `producer_qmt_connection_status` 指标

2. **RabbitMQ连接失败**
   - 检查RabbitMQ服务状态
   - 验证连接参数
   - 查看 `producer_rabbitmq_connection_status` 指标

3. **系统资源不足**
   - 监控CPU和内存使用率
   - 检查磁盘空间
   - 查看系统指标

4. **消息发送失败**
   - 查看错误日志
   - 检查网络连接
   - 监控 `producer_errors_total` 指标

### 监控告警建议

建议设置以下Prometheus告警规则：

```yaml
groups:
  - name: producer_alerts
    rules:
      - alert: ProducerServiceDown
        expr: producer_service_status == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Producer service is down"
      
      - alert: HighCPUUsage
        expr: producer_cpu_usage_percent > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High CPU usage on producer"
      
      - alert: HighMemoryUsage
        expr: producer_memory_usage_percent > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage on producer"
      
      - alert: RabbitMQConnectionLost
        expr: producer_rabbitmq_connection_status == 0
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "RabbitMQ connection lost"
```

## 部署建议

### 1. 容器化部署

可以将producer服务容器化，与consumer服务一起部署：

```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .
EXPOSE 8081 8001

CMD ["python", "producer/collect_data.py"]
```

### 2. 监控集成

- 配置Prometheus抓取指标
- 设置Grafana仪表板
- 配置告警规则

### 3. 高可用部署

- 部署多个producer实例
- 使用负载均衡
- 实现故障转移

## 许可证

MIT License
