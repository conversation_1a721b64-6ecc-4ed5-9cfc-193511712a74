"""
MySQL数据库管理器
用于处理市场数据的存储和查询
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import yaml
import pymysql
from pymysql.cursors import DictCursor
from contextlib import contextmanager
import threading
from queue import Queue, Empty
import json


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self, config_path: str = "config/database.yaml"):
        """
        初始化数据库管理器
        
        Args:
            config_path: 数据库配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        self._connection_pool = Queue(maxsize=self.config['database']['pool']['max_connections'])
        self._pool_lock = threading.Lock()
        self._init_connection_pool()
        
        # 批量处理队列
        self._batch_queue = Queue()
        self._batch_thread = None
        self._stop_batch_processing = False
        
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            raise FileNotFoundError(f"数据库配置文件 {config_path} 不存在")
        except yaml.YAMLError as e:
            raise ValueError(f"配置文件格式错误: {e}")
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logger = logging.getLogger(__name__)
        logger.setLevel(logging.INFO)
        
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        
        return logger
    
    def _create_connection(self) -> pymysql.Connection:
        """创建数据库连接"""
        db_config = self.config['database']['primary']
        conn_config = self.config['database']['connection']
        
        return pymysql.connect(
            host=db_config['host'],
            port=db_config['port'],
            user=db_config['username'],
            password=db_config['password'],
            database=db_config['database'],
            charset=db_config['charset'],
            connect_timeout=conn_config['connect_timeout'],
            read_timeout=conn_config['read_timeout'],
            write_timeout=conn_config['write_timeout'],
            autocommit=conn_config['autocommit'],
            cursorclass=DictCursor
        )
    
    def _init_connection_pool(self):
        """初始化连接池"""
        min_connections = self.config['database']['pool']['min_connections']
        
        for _ in range(min_connections):
            try:
                conn = self._create_connection()
                self._connection_pool.put(conn)
            except Exception as e:
                self.logger.error(f"创建数据库连接失败: {e}")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器）"""
        conn = None
        try:
            # 尝试从连接池获取连接
            try:
                conn = self._connection_pool.get_nowait()
                # 检查连接是否有效
                conn.ping(reconnect=True)
            except Empty:
                # 连接池为空，创建新连接
                conn = self._create_connection()
            
            yield conn
            
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"数据库操作错误: {e}")
            raise
        finally:
            if conn:
                try:
                    # 将连接放回连接池
                    if self._connection_pool.qsize() < self.config['database']['pool']['max_connections']:
                        self._connection_pool.put(conn)
                    else:
                        conn.close()
                except Exception as e:
                    self.logger.error(f"归还连接到连接池失败: {e}")
                    if conn:
                        conn.close()
    
    def insert_market_quote(self, symbol: str, quote_data: Dict[str, Any]) -> bool:
        """
        插入市场行情数据
        
        Args:
            symbol: 股票代码
            quote_data: 行情数据
            
        Returns:
            bool: 插入是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 插入主行情数据
                quote_sql = """
                INSERT INTO market_quotes (
                    symbol, time, timetag, last_price, open_price, high_price, low_price,
                    last_close, amount, volume, pvolume, stock_status, open_int,
                    settlement_price, last_settlement_price
                ) VALUES (
                    %(symbol)s, %(time)s, %(timetag)s, %(lastPrice)s, %(open)s, %(high)s, %(low)s,
                    %(lastClose)s, %(amount)s, %(volume)s, %(pvolume)s, %(stockStatus)s, %(openInt)s,
                    %(settlementPrice)s, %(lastSettlementPrice)s
                ) ON DUPLICATE KEY UPDATE
                    last_price=VALUES(last_price), open_price=VALUES(open_price),
                    high_price=VALUES(high_price), low_price=VALUES(low_price),
                    last_close=VALUES(last_close), amount=VALUES(amount),
                    volume=VALUES(volume), pvolume=VALUES(pvolume),
                    stock_status=VALUES(stock_status), open_int=VALUES(open_int),
                    settlement_price=VALUES(settlement_price),
                    last_settlement_price=VALUES(last_settlement_price),
                    updated_at=CURRENT_TIMESTAMP
                """
                
                quote_params = {
                    'symbol': symbol,
                    **quote_data
                }
                
                cursor.execute(quote_sql, quote_params)
                
                # 插入盘口数据
                depth_sql = """
                INSERT INTO market_depth (
                    symbol, time, timetag,
                    ask_price_1, ask_price_2, ask_price_3, ask_price_4, ask_price_5,
                    bid_price_1, bid_price_2, bid_price_3, bid_price_4, bid_price_5,
                    ask_vol_1, ask_vol_2, ask_vol_3, ask_vol_4, ask_vol_5,
                    bid_vol_1, bid_vol_2, bid_vol_3, bid_vol_4, bid_vol_5
                ) VALUES (
                    %(symbol)s, %(time)s, %(timetag)s,
                    %(ask_price_1)s, %(ask_price_2)s, %(ask_price_3)s, %(ask_price_4)s, %(ask_price_5)s,
                    %(bid_price_1)s, %(bid_price_2)s, %(bid_price_3)s, %(bid_price_4)s, %(bid_price_5)s,
                    %(ask_vol_1)s, %(ask_vol_2)s, %(ask_vol_3)s, %(ask_vol_4)s, %(ask_vol_5)s,
                    %(bid_vol_1)s, %(bid_vol_2)s, %(bid_vol_3)s, %(bid_vol_4)s, %(bid_vol_5)s
                ) ON DUPLICATE KEY UPDATE
                    ask_price_1=VALUES(ask_price_1), ask_price_2=VALUES(ask_price_2),
                    ask_price_3=VALUES(ask_price_3), ask_price_4=VALUES(ask_price_4),
                    ask_price_5=VALUES(ask_price_5), bid_price_1=VALUES(bid_price_1),
                    bid_price_2=VALUES(bid_price_2), bid_price_3=VALUES(bid_price_3),
                    bid_price_4=VALUES(bid_price_4), bid_price_5=VALUES(bid_price_5),
                    ask_vol_1=VALUES(ask_vol_1), ask_vol_2=VALUES(ask_vol_2),
                    ask_vol_3=VALUES(ask_vol_3), ask_vol_4=VALUES(ask_vol_4),
                    ask_vol_5=VALUES(ask_vol_5), bid_vol_1=VALUES(bid_vol_1),
                    bid_vol_2=VALUES(bid_vol_2), bid_vol_3=VALUES(bid_vol_3),
                    bid_vol_4=VALUES(bid_vol_4), bid_vol_5=VALUES(bid_vol_5)
                """
                
                depth_params = {
                    'symbol': symbol,
                    'time': quote_data.get('time'),
                    'timetag': quote_data.get('timetag'),
                }
                
                # 处理买卖盘数据
                ask_prices = quote_data.get('askPrice', [0, 0, 0, 0, 0])
                bid_prices = quote_data.get('bidPrice', [0, 0, 0, 0, 0])
                ask_vols = quote_data.get('askVol', [0, 0, 0, 0, 0])
                bid_vols = quote_data.get('bidVol', [0, 0, 0, 0, 0])
                
                for i in range(5):
                    depth_params[f'ask_price_{i+1}'] = ask_prices[i] if i < len(ask_prices) else 0
                    depth_params[f'bid_price_{i+1}'] = bid_prices[i] if i < len(bid_prices) else 0
                    depth_params[f'ask_vol_{i+1}'] = ask_vols[i] if i < len(ask_vols) else 0
                    depth_params[f'bid_vol_{i+1}'] = bid_vols[i] if i < len(bid_vols) else 0
                
                cursor.execute(depth_sql, depth_params)
                
                conn.commit()
                self.logger.debug(f"成功插入 {symbol} 的行情数据")
                return True
                
        except Exception as e:
            self.logger.error(f"插入行情数据失败 {symbol}: {e}")
            return False
    
    def log_processing_result(self, message_id: str, symbol: str, status: str, 
                            error_message: str = None, raw_data: Dict = None) -> bool:
        """
        记录处理结果
        
        Args:
            message_id: 消息ID
            symbol: 股票代码
            status: 处理状态
            error_message: 错误信息
            raw_data: 原始数据
            
        Returns:
            bool: 记录是否成功
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = """
                INSERT INTO processing_log (
                    message_id, symbol, status, error_message, raw_data
                ) VALUES (
                    %s, %s, %s, %s, %s
                )
                """
                
                cursor.execute(sql, (
                    message_id,
                    symbol,
                    status,
                    error_message,
                    json.dumps(raw_data) if raw_data else None
                ))
                
                conn.commit()
                return True
                
        except Exception as e:
            self.logger.error(f"记录处理日志失败: {e}")
            return False
    
    def get_latest_quotes(self, symbols: List[str] = None) -> List[Dict[str, Any]]:
        """
        获取最新行情数据
        
        Args:
            symbols: 股票代码列表，为None时获取所有
            
        Returns:
            List[Dict]: 行情数据列表
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                if symbols:
                    placeholders = ','.join(['%s'] * len(symbols))
                    sql = f"SELECT * FROM latest_quotes WHERE symbol IN ({placeholders})"
                    cursor.execute(sql, symbols)
                else:
                    sql = "SELECT * FROM latest_quotes"
                    cursor.execute(sql)
                
                return cursor.fetchall()
                
        except Exception as e:
            self.logger.error(f"查询最新行情失败: {e}")
            return []
    
    def health_check(self) -> Dict[str, Any]:
        """
        数据库健康检查
        
        Returns:
            Dict: 健康状态信息
        """
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 检查连接
                cursor.execute("SELECT 1")
                
                # 检查表状态
                cursor.execute("SHOW TABLE STATUS LIKE 'market_quotes'")
                table_status = cursor.fetchone()
                
                # 检查最新数据时间
                cursor.execute("SELECT MAX(created_at) as latest_time FROM market_quotes")
                latest_data = cursor.fetchone()
                
                return {
                    'status': 'healthy',
                    'connection': 'ok',
                    'table_rows': table_status.get('Rows', 0) if table_status else 0,
                    'latest_data_time': latest_data.get('latest_time') if latest_data else None,
                    'pool_size': self._connection_pool.qsize()
                }
                
        except Exception as e:
            self.logger.error(f"数据库健康检查失败: {e}")
            return {
                'status': 'unhealthy',
                'error': str(e)
            }
    
    def close(self):
        """关闭数据库连接池"""
        self._stop_batch_processing = True
        
        # 关闭所有连接
        while not self._connection_pool.empty():
            try:
                conn = self._connection_pool.get_nowait()
                conn.close()
            except Empty:
                break
            except Exception as e:
                self.logger.error(f"关闭数据库连接失败: {e}")


# 全局数据库管理器实例
_db_manager = None


def get_database_manager(config_path: str = "config/database.yaml") -> DatabaseManager:
    """获取数据库管理器单例"""
    global _db_manager
    if _db_manager is None:
        _db_manager = DatabaseManager(config_path)
    return _db_manager


def close_database_manager():
    """关闭数据库管理器"""
    global _db_manager
    if _db_manager:
        _db_manager.close()
        _db_manager = None
