2025-06-20 13:47:54,254 - stock_config_loader - INFO - 成功加载股票配置文件: ../config/stocks_config.yaml
2025-06-20 13:47:54,504 - stock_config_loader - INFO - 加载股票组 'default_list': 30 只股票
2025-06-20 13:47:54,761 - __main__ - INFO - 从配置文件加载了 30 只股票
2025-06-20 13:47:54,978 - __main__ - INFO - 订阅配置: period=tick, count=0
2025-06-20 13:47:55,446 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 13:47:55,489 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:47:55,501 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 13:47:56,449 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:47:56,457 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:47:56,466 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:47:56,471 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 13:47:56,630 - rabbitmq_client - INFO - 成功连接到RabbitMQ服务器
2025-06-20 13:47:56,641 - __main__ - INFO - 启动市场数据生产者服务...
2025-06-20 13:47:56,643 - __main__ - INFO - 启动Prometheus指标收集器...
2025-06-20 13:47:57,042 - producer_metrics - INFO - Producer Prometheus指标服务器已启动，端口: 8001
2025-06-20 13:47:57,113 - producer_metrics - INFO - 系统指标收集线程已启动
2025-06-20 13:47:57,138 - __main__ - INFO - 启动健康检查服务器...
2025-06-20 13:47:57,216 - producer_health - INFO - Producer健康检查服务器已启动: http://0.0.0.0:8081
2025-06-20 13:47:57,256 - producer_health - INFO - 可用端点:
2025-06-20 13:47:57,294 - producer_health - INFO -   GET /health - 基本健康检查
2025-06-20 13:47:57,322 - producer_health - INFO -   GET /health/live - 存活性检查
2025-06-20 13:47:57,333 - producer_health - INFO -   GET /health/ready - 就绪性检查
2025-06-20 13:47:57,339 - producer_health - INFO -   GET /health/detailed - 详细健康检查
2025-06-20 13:47:57,343 - producer_health - INFO -   GET /stats - 统计信息
2025-06-20 13:47:57,352 - producer_health - INFO -   GET /system - 系统信息
2025-06-20 13:47:57,356 - producer_health - INFO -   GET /metrics - Prometheus指标重定向
2025-06-20 13:47:57,359 - __main__ - INFO - 监控服务初始化完成
2025-06-20 13:47:57,363 - __main__ - INFO - Prometheus指标: http://localhost:8001/metrics
2025-06-20 13:47:57,383 - __main__ - INFO - 健康检查: http://localhost:8081/health
2025-06-20 13:47:57,399 - __main__ - INFO - RabbitMQ连接测试成功
2025-06-20 13:47:57,403 - __main__ - INFO - 准备订阅 30 只股票
2025-06-20 13:47:57,404 - __main__ - INFO - 开始订阅行情数据...
2025-06-20 13:47:57,407 - __main__ - INFO - 订阅参数: period=tick, count=0, delay=0.1s
2025-06-20 13:47:57,639 - __main__ - INFO - 成功订阅股票: 600030.SH
2025-06-20 13:47:57,816 - __main__ - INFO - 成功订阅股票: 600061.SH
2025-06-20 13:47:57,976 - __main__ - INFO - 成功订阅股票: 600109.SH
2025-06-20 13:47:58,124 - __main__ - INFO - 成功订阅股票: 600918.SH
2025-06-20 13:47:58,271 - __main__ - INFO - 成功订阅股票: 600958.SH
2025-06-20 13:47:58,430 - __main__ - INFO - 成功订阅股票: 600999.SH
2025-06-20 13:47:58,573 - __main__ - INFO - 成功订阅股票: 601059.SH
2025-06-20 13:47:58,775 - __main__ - INFO - 成功订阅股票: 601066.SH
2025-06-20 13:47:58,987 - __main__ - INFO - 成功订阅股票: 601108.SH
2025-06-20 13:47:59,170 - __main__ - INFO - 成功订阅股票: 601136.SH
2025-06-20 13:47:59,325 - __main__ - INFO - 成功订阅股票: 601211.SH
2025-06-20 13:47:59,581 - __main__ - INFO - 成功订阅股票: 601236.SH
2025-06-20 13:47:59,731 - __main__ - INFO - 成功订阅股票: 601377.SH
2025-06-20 13:47:59,873 - __main__ - INFO - 成功订阅股票: 601456.SH
2025-06-20 13:48:00,034 - __main__ - INFO - 成功订阅股票: 601555.SH
2025-06-20 13:48:00,197 - __main__ - INFO - 成功订阅股票: 601688.SH
2025-06-20 13:48:00,345 - __main__ - INFO - 成功订阅股票: 601788.SH
2025-06-20 13:48:00,500 - __main__ - INFO - 成功订阅股票: 601878.SH
2025-06-20 13:48:00,658 - __main__ - INFO - 成功订阅股票: 601881.SH
2025-06-20 13:48:00,853 - __main__ - INFO - 成功订阅股票: 601901.SH
2025-06-20 13:48:00,998 - __main__ - INFO - 成功订阅股票: 601990.SH
2025-06-20 13:48:01,134 - __main__ - INFO - 成功订阅股票: 601995.SH
2025-06-20 13:48:01,282 - __main__ - INFO - 成功订阅股票: 000166.SZ
2025-06-20 13:48:01,425 - __main__ - INFO - 成功订阅股票: 000776.SZ
2025-06-20 13:48:01,577 - __main__ - INFO - 成功订阅股票: 000783.SZ
2025-06-20 13:48:01,724 - __main__ - INFO - 成功订阅股票: 002673.SZ
2025-06-20 13:48:01,876 - __main__ - INFO - 成功订阅股票: 002736.SZ
2025-06-20 13:48:02,032 - __main__ - INFO - 成功订阅股票: 002939.SZ
2025-06-20 13:48:02,203 - __main__ - INFO - 成功订阅股票: 002945.SZ
2025-06-20 13:48:02,371 - __main__ - INFO - 成功订阅股票: 300059.SZ
2025-06-20 13:48:02,486 - __main__ - INFO - 订阅完成: 30/30 只股票订阅成功
2025-06-20 13:48:02,486 - __main__ - INFO - 数据订阅服务已启动
2025-06-20 13:48:02,486 - __main__ - INFO - 监控端点:
2025-06-20 13:48:02,486 - __main__ - INFO -   健康检查: http://localhost:8081/health
2025-06-20 13:48:02,487 - __main__ - INFO -   系统信息: http://localhost:8081/system
2025-06-20 13:48:02,487 - __main__ - INFO -   Prometheus指标: http://localhost:8001/metrics
2025-06-20 13:48:02,488 - __main__ - INFO - 按 CTRL+C 停止服务
2025-06-20 13:48:32,897 - __main__ - INFO - 接收到信号 2，开始优雅关闭...
2025-06-20 13:48:32,897 - __main__ - INFO - 开始清理资源...
2025-06-20 13:48:32,898 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 13:48:32,898 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 13:48:32,901 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 13:48:32,902 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 13:48:32,903 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,904 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,905 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,905 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 13:48:32,907 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 13:48:32,907 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,908 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 13:48:32,909 - rabbitmq_client - INFO - 已断开RabbitMQ连接
2025-06-20 13:48:32,952 - producer_health - INFO - Producer健康检查服务器已停止
2025-06-20 13:48:32,952 - __main__ - INFO - 资源清理完成，服务已关闭
