2025-06-20 13:52:33 - encoding_test - INFO - 🔧 日志编码修复测试开始
2025-06-20 13:52:33 - encoding_test - INFO - 📊 股票代码: 600000.SH (浦发银行)
2025-06-20 13:52:33 - encoding_test - INFO - ✅ 订阅成功: 中信证券
2025-06-20 13:52:33 - encoding_test - INFO - ⚠️ 警告: 网络连接不稳定
2025-06-20 13:52:33 - encoding_test - INFO - ❌ 错误: 数据解析失败
2025-06-20 13:52:33 - encoding_test - INFO - 🎉 修复完成: 日志编码问题已解决
2025-06-20 13:53:43 - stock_config_loader - INFO - 成功加载股票配置文件: ../config/stocks_config.yaml
2025-06-20 13:53:43 - stock_config_loader - INFO - 加载股票组 'default_list': 30 只股票
2025-06-20 13:53:43 - main - INFO - 从配置文件加载了 30 只股票
2025-06-20 13:53:44 - main - INFO - 订阅配置: period=tick, count=0
2025-06-20 13:53:44 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 13:53:44 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=3536, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 49227), raddr=('127.0.0.1', 5672)>
2025-06-20 13:53:44 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000178CA1F4070>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000178CA1F4070> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 13:53:44 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000178CA1F4070> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:53:44 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000178CA1F4070> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:53:44 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000178CA1F4070> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:53:44 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 13:53:44 - rabbitmq_client - INFO - 成功连接到RabbitMQ服务器
2025-06-20 13:53:44 - producer_test - INFO - 🚀 Producer服务启动
2025-06-20 13:53:44 - producer_test - INFO - 📊 从配置文件加载了 30 只股票
2025-06-20 13:53:44 - producer_test - INFO - ✅ 成功订阅股票: 600030.SH (中信证券)
2025-06-20 13:53:44 - producer_test - INFO - ✅ 成功订阅股票: 600000.SH (浦发银行)
2025-06-20 13:53:44 - producer_test - INFO - 📤 数据已发送到RabbitMQ: 1024 bytes, 耗时: 0.025s
2025-06-20 13:53:44 - producer_test - WARNING - ⚠️ RabbitMQ连接不稳定，正在重试...
2025-06-20 13:53:44 - producer_test - ERROR - ❌ 订阅股票 000001.SZ 失败: 网络超时
2025-06-20 13:53:45 - producer_test - INFO - 📈 监控指标更新: CPU 45.2%, 内存 67.8%
2025-06-20 13:53:45 - producer_test - INFO - 🔄 已处理 1000 条行情数据
2025-06-20 13:53:45 - producer_test - INFO - 💾 数据库连接池状态: 5/20 连接
2025-06-20 13:53:45 - emoji_test - INFO - 字符测试: 🚀🔧📊✅❌⚠️💾📈📤🔍
2025-06-20 13:53:45 - emoji_test - INFO - 字符测试: 中文字符测试
2025-06-20 13:53:45 - emoji_test - INFO - 字符测试: 股票代码: 600000.SH
2025-06-20 13:53:45 - emoji_test - INFO - 字符测试: 数据处理: 成功
2025-06-20 13:53:45 - emoji_test - INFO - 字符测试: 错误信息: 连接失败
2025-06-20 13:53:45 - emoji_test - INFO - 字符测试: Mixed: 🎉 成功处理 1000 条数据
