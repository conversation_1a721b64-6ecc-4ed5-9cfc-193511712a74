2025-06-20 10:39:59,186 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('::1', 5672, 0, 0)
2025-06-20 10:39:59,187 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=1964, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61486, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:39:59,188 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002659E1D0EE0>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002659E1D0EE0> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:39:59,241 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002659E1D0EE0> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:39:59,242 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002659E1D0EE0> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:39:59,244 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002659E1D0EE0> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:39:59,247 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:39:59,297 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:39:59,299 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:39:59,300 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:39:59,363 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:39:59,364 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:39:59,364 - __main__ - INFO - ����������������...
2025-06-20 10:39:59,371 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:39:59,372 - producer_health - INFO - ���ö˵�:
2025-06-20 10:39:59,373 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:39:59,375 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:39:59,376 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:39:59,377 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:39:59,378 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:39:59,379 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:39:59,379 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:39:59,381 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:39:59,382 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:39:59,383 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:39:59,386 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:39:59,388 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:39:59,389 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:40:01,437 - __main__ - ERROR - ���й����з�������: �޷������������
2025-06-20 10:40:01,462 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 10:40:01,463 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 10:40:01,464 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002659E1D0EE0> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:40:01,470 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002659E1D0EE0> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:40:01,472 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 10:40:01,475 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=1964, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61486, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:01,476 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=1964, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61486, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:01,477 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=1964, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61486, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:01,479 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:40:01,480 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:40:01,482 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=1964, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61486, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:01,483 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 10:40:01,486 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 10:40:01,934 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 10:40:01,935 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 10:40:13,355 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('::1', 5672, 0, 0)
2025-06-20 10:40:13,356 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=2012, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61507, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:13,357 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000126B76E2500>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000126B76E2500> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:40:13,418 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000126B76E2500> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:40:13,419 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000126B76E2500> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:40:13,420 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000126B76E2500> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:40:13,422 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:40:13,431 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:40:13,432 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:40:13,432 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:40:13,449 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:40:13,450 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:40:13,451 - __main__ - INFO - ����������������...
2025-06-20 10:40:13,452 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:40:13,453 - producer_health - INFO - ���ö˵�:
2025-06-20 10:40:13,453 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:40:13,454 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:40:13,454 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:40:13,454 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:40:13,455 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:40:13,455 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:40:13,455 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:40:13,455 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:40:13,456 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:40:13,456 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:40:13,457 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:40:13,457 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:40:13,458 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:40:15,495 - __main__ - ERROR - ���й����з�������: �޷������������
2025-06-20 10:40:15,500 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 10:40:15,562 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 10:40:15,563 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000126B76E2500> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:40:15,569 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000126B76E2500> params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:40:15,571 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 10:40:15,576 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=2012, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61507, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:15,578 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=2012, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61507, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:15,580 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=2012, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61507, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:15,582 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:40:15,585 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:40:15,587 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=2012, family=AddressFamily.AF_INET6, type=SocketKind.SOCK_STREAM, proto=6, laddr=('::1', 61507, 0, 0), raddr=('::1', 5672, 0, 0)>
2025-06-20 10:40:15,589 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=localhost port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 10:40:15,593 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 10:40:15,985 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 10:40:15,986 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 10:40:51,467 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 10:40:51,468 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=2068, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 61633), raddr=('127.0.0.1', 5672)>
2025-06-20 10:40:51,469 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001D9F1862500>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001D9F1862500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:40:51,475 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001D9F1862500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:40:51,476 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001D9F1862500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:40:51,476 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001D9F1862500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:40:51,477 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:40:51,485 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:40:51,486 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:40:51,486 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:40:51,511 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:40:51,513 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:40:51,513 - __main__ - INFO - ����������������...
2025-06-20 10:40:51,517 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:40:51,517 - producer_health - INFO - ���ö˵�:
2025-06-20 10:40:51,518 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:40:51,518 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:40:51,519 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:40:51,519 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:40:51,519 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:40:51,520 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:40:51,520 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:40:51,520 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:40:51,521 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:40:51,521 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:40:51,522 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:40:51,522 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:40:51,522 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:40:53,576 - __main__ - ERROR - ���й����з�������: �޷������������
2025-06-20 10:40:53,621 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 10:40:53,622 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 10:40:53,623 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001D9F1862500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:40:53,629 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001D9F1862500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:40:53,632 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 10:40:53,637 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=2068, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 61633), raddr=('127.0.0.1', 5672)>
2025-06-20 10:40:53,640 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=2068, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 61633), raddr=('127.0.0.1', 5672)>
2025-06-20 10:40:53,641 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=2068, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 61633), raddr=('127.0.0.1', 5672)>
2025-06-20 10:40:53,643 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:40:53,644 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:40:53,647 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=2068, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 61633), raddr=('127.0.0.1', 5672)>
2025-06-20 10:40:53,649 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 10:40:53,651 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 10:40:54,076 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 10:40:54,076 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 10:46:15,052 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 10:46:15,053 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62362), raddr=('127.0.0.1', 5672)>
2025-06-20 10:46:15,054 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017581672500>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017581672500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:46:15,062 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017581672500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:46:15,063 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017581672500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:46:15,065 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017581672500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:46:15,066 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:46:15,075 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:46:15,075 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:46:15,076 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:46:15,115 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:46:15,116 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:46:15,117 - __main__ - INFO - ����������������...
2025-06-20 10:46:15,119 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:46:15,119 - producer_health - INFO - ���ö˵�:
2025-06-20 10:46:15,119 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:46:15,119 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:46:15,120 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:46:15,120 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:46:15,120 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:46:15,120 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:46:15,121 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:46:15,121 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:46:15,121 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:46:15,122 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:46:15,124 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:46:15,124 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:46:15,124 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:46:15,135 - __main__ - INFO - ���ݶ��ķ���������
2025-06-20 10:46:15,135 - __main__ - INFO - ��ض˵�:
2025-06-20 10:46:15,135 - __main__ - INFO -   �������: http://localhost:8081/health
2025-06-20 10:46:15,136 - __main__ - INFO -   ϵͳ��Ϣ: http://localhost:8081/system
2025-06-20 10:46:15,136 - __main__ - INFO -   Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:46:15,136 - __main__ - INFO - �� CTRL+C ֹͣ����
2025-06-20 10:47:40,852 - __main__ - INFO - ���յ��ź� 2����ʼ���Źر�...
2025-06-20 10:47:40,856 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 10:47:41,129 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 10:47:41,254 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017581672500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:47:41,356 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000017581672500> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:47:41,360 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 10:47:41,461 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62362), raddr=('127.0.0.1', 5672)>
2025-06-20 10:47:41,465 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62362), raddr=('127.0.0.1', 5672)>
2025-06-20 10:47:41,470 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62362), raddr=('127.0.0.1', 5672)>
2025-06-20 10:47:41,470 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:47:41,474 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:47:41,475 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62362), raddr=('127.0.0.1', 5672)>
2025-06-20 10:47:41,477 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 10:47:41,483 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 10:47:41,784 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 10:47:41,789 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 10:49:27,906 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 10:49:27,907 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=1920, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62837), raddr=('127.0.0.1', 5672)>
2025-06-20 10:49:27,907 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002269B522650>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002269B522650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:49:27,914 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002269B522650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:49:27,915 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002269B522650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:49:27,915 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002269B522650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:49:27,916 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:49:27,922 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:49:27,923 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:49:27,923 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:49:27,941 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:49:27,942 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:49:27,942 - __main__ - INFO - ����������������...
2025-06-20 10:49:27,943 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:49:27,944 - producer_health - INFO - ���ö˵�:
2025-06-20 10:49:27,944 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:49:27,944 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:49:27,944 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:49:27,944 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:49:27,944 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:49:27,945 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:49:27,945 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:49:27,945 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:49:27,945 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:49:27,946 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:49:27,947 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:49:27,947 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:49:27,947 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:49:28,031 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600030.SH
2025-06-20 10:49:28,215 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600061.SH
2025-06-20 10:49:28,428 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600109.SH
2025-06-20 10:49:28,593 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600918.SH
2025-06-20 10:49:28,766 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600958.SH
2025-06-20 10:49:28,919 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600999.SH
2025-06-20 10:49:29,065 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601059.SH
2025-06-20 10:49:29,222 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601066.SH
2025-06-20 10:49:29,421 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601108.SH
2025-06-20 10:49:29,641 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601136.SH
2025-06-20 10:49:29,844 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601211.SH
2025-06-20 10:49:29,996 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601236.SH
2025-06-20 10:49:30,203 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601377.SH
2025-06-20 10:49:30,359 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601456.SH
2025-06-20 10:49:30,535 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601555.SH
2025-06-20 10:49:30,745 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601688.SH
2025-06-20 10:49:30,919 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601788.SH
2025-06-20 10:49:31,160 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601878.SH
2025-06-20 10:49:31,360 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601881.SH
2025-06-20 10:49:31,697 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601901.SH
2025-06-20 10:49:31,871 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601990.SH
2025-06-20 10:49:32,045 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601995.SH
2025-06-20 10:49:32,218 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000166.SZ
2025-06-20 10:49:32,386 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000776.SZ
2025-06-20 10:49:32,548 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000783.SZ
2025-06-20 10:49:32,724 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002673.SZ
2025-06-20 10:49:32,891 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002736.SZ
2025-06-20 10:49:33,058 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002939.SZ
2025-06-20 10:49:33,234 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002945.SZ
2025-06-20 10:49:33,402 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 300059.SZ
2025-06-20 10:49:33,505 - __main__ - INFO - �������: 30/30 ֻ��Ʊ���ĳɹ�
2025-06-20 10:49:33,505 - __main__ - INFO - ���ݶ��ķ���������
2025-06-20 10:49:33,505 - __main__ - INFO - ��ض˵�:
2025-06-20 10:49:33,505 - __main__ - INFO -   �������: http://localhost:8081/health
2025-06-20 10:49:33,505 - __main__ - INFO -   ϵͳ��Ϣ: http://localhost:8081/system
2025-06-20 10:49:33,506 - __main__ - INFO -   Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:49:33,506 - __main__ - INFO - �� CTRL+C ֹͣ����
2025-06-20 10:49:42,833 - __main__ - INFO - ���յ��ź� 2����ʼ���Źر�...
2025-06-20 10:49:42,833 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 10:49:42,834 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 10:49:42,834 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002269B522650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:49:42,837 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002269B522650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:49:42,837 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 10:49:42,839 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=1920, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62837), raddr=('127.0.0.1', 5672)>
2025-06-20 10:49:42,839 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=1920, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62837), raddr=('127.0.0.1', 5672)>
2025-06-20 10:49:42,840 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=1920, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62837), raddr=('127.0.0.1', 5672)>
2025-06-20 10:49:42,840 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:49:42,841 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:49:42,842 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=1920, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62837), raddr=('127.0.0.1', 5672)>
2025-06-20 10:49:42,842 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 10:49:42,843 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 10:49:42,951 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 10:49:42,951 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 10:49:54,083 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 10:49:54,084 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=1980, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 62928), raddr=('127.0.0.1', 5672)>
2025-06-20 10:49:54,085 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002972C232650>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002972C232650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:49:54,093 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002972C232650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:49:54,093 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002972C232650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:49:54,094 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002972C232650> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:49:54,094 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:49:54,101 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:49:54,102 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:49:54,103 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:49:54,124 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:49:54,125 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:49:54,125 - __main__ - INFO - ����������������...
2025-06-20 10:49:54,127 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:49:54,127 - producer_health - INFO - ���ö˵�:
2025-06-20 10:49:54,128 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:49:54,129 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:49:54,129 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:49:54,129 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:49:54,129 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:49:54,130 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:49:54,130 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:49:54,130 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:49:54,130 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:49:54,130 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:49:54,131 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:49:54,131 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:49:54,132 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:49:54,211 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600030.SH
2025-06-20 10:49:54,399 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600061.SH
2025-06-20 10:49:54,572 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600109.SH
2025-06-20 10:49:54,752 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600918.SH
2025-06-20 10:49:54,921 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600958.SH
2025-06-20 10:49:55,093 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600999.SH
2025-06-20 10:49:55,258 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601059.SH
2025-06-20 10:49:55,414 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601066.SH
2025-06-20 10:49:55,599 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601108.SH
2025-06-20 10:49:55,755 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601136.SH
2025-06-20 10:49:55,919 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601211.SH
2025-06-20 10:49:56,103 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601236.SH
2025-06-20 10:49:56,257 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601377.SH
2025-06-20 10:49:56,415 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601456.SH
2025-06-20 10:49:56,593 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601555.SH
2025-06-20 10:49:56,782 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601688.SH
2025-06-20 10:49:56,962 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601788.SH
2025-06-20 10:49:57,146 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601878.SH
2025-06-20 10:49:57,303 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601881.SH
2025-06-20 10:49:57,461 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601901.SH
2025-06-20 10:49:57,648 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601990.SH
2025-06-20 10:49:57,828 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601995.SH
2025-06-20 10:49:57,987 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000166.SZ
2025-06-20 10:49:58,165 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000776.SZ
2025-06-20 10:49:58,503 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000783.SZ
2025-06-20 10:49:58,690 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002673.SZ
2025-06-20 10:49:58,853 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002736.SZ
2025-06-20 10:49:59,026 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002939.SZ
2025-06-20 10:49:59,212 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002945.SZ
2025-06-20 10:49:59,383 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 300059.SZ
2025-06-20 10:49:59,494 - __main__ - INFO - �������: 30/30 ֻ��Ʊ���ĳɹ�
2025-06-20 10:49:59,494 - __main__ - INFO - ���ݶ��ķ���������
2025-06-20 10:49:59,494 - __main__ - INFO - ��ض˵�:
2025-06-20 10:49:59,494 - __main__ - INFO -   �������: http://localhost:8081/health
2025-06-20 10:49:59,495 - __main__ - INFO -   ϵͳ��Ϣ: http://localhost:8081/system
2025-06-20 10:49:59,495 - __main__ - INFO -   Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:49:59,495 - __main__ - INFO - �� CTRL+C ֹͣ����
2025-06-20 10:54:17,668 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 10:54:17,670 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=1944, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64616), raddr=('127.0.0.1', 5672)>
2025-06-20 10:54:17,671 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000235023426B0>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000235023426B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:54:17,676 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000235023426B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:54:17,677 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000235023426B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:54:17,678 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000235023426B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:54:17,678 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:54:17,686 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:54:17,686 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:54:17,687 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:54:17,784 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:54:17,797 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:54:17,797 - __main__ - INFO - ����������������...
2025-06-20 10:54:17,800 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:54:17,800 - producer_health - INFO - ���ö˵�:
2025-06-20 10:54:17,801 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:54:17,801 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:54:17,801 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:54:17,801 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:54:17,802 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:54:17,802 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:54:17,802 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:54:17,802 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:54:17,803 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:54:17,803 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:54:17,805 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:54:17,805 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:54:17,805 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:54:17,861 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600030.SH
2025-06-20 10:54:18,025 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600061.SH
2025-06-20 10:54:18,197 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600109.SH
2025-06-20 10:54:18,383 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600918.SH
2025-06-20 10:54:18,561 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600958.SH
2025-06-20 10:54:18,831 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600999.SH
2025-06-20 10:54:19,099 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601059.SH
2025-06-20 10:54:19,279 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601066.SH
2025-06-20 10:54:19,562 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601108.SH
2025-06-20 10:54:19,780 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601136.SH
2025-06-20 10:54:19,949 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601211.SH
2025-06-20 10:54:20,155 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601236.SH
2025-06-20 10:54:20,318 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601377.SH
2025-06-20 10:54:20,519 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601456.SH
2025-06-20 10:54:20,821 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601555.SH
2025-06-20 10:54:20,993 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601688.SH
2025-06-20 10:54:21,174 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601788.SH
2025-06-20 10:54:21,354 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601878.SH
2025-06-20 10:54:21,503 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601881.SH
2025-06-20 10:54:21,718 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601901.SH
2025-06-20 10:54:21,913 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601990.SH
2025-06-20 10:54:22,103 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601995.SH
2025-06-20 10:54:22,257 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000166.SZ
2025-06-20 10:54:22,439 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000776.SZ
2025-06-20 10:54:22,629 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000783.SZ
2025-06-20 10:54:22,889 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002673.SZ
2025-06-20 10:54:23,104 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002736.SZ
2025-06-20 10:54:23,267 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002939.SZ
2025-06-20 10:54:23,450 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002945.SZ
2025-06-20 10:54:23,643 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 300059.SZ
2025-06-20 10:54:23,752 - __main__ - INFO - �������: 30/30 ֻ��Ʊ���ĳɹ�
2025-06-20 10:54:23,752 - __main__ - INFO - ���ݶ��ķ���������
2025-06-20 10:54:23,752 - __main__ - INFO - ��ض˵�:
2025-06-20 10:54:23,753 - __main__ - INFO -   �������: http://localhost:8081/health
2025-06-20 10:54:23,753 - __main__ - INFO -   ϵͳ��Ϣ: http://localhost:8081/system
2025-06-20 10:54:23,753 - __main__ - INFO -   Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:54:23,753 - __main__ - INFO - �� CTRL+C ֹͣ����
2025-06-20 10:54:27,588 - __main__ - INFO - ���յ��ź� 2����ʼ���Źر�...
2025-06-20 10:54:27,588 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 10:54:27,589 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 10:54:27,589 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000235023426B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:54:27,592 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x00000235023426B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:54:27,593 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 10:54:27,595 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=1944, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64616), raddr=('127.0.0.1', 5672)>
2025-06-20 10:54:27,595 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=1944, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64616), raddr=('127.0.0.1', 5672)>
2025-06-20 10:54:27,595 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=1944, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64616), raddr=('127.0.0.1', 5672)>
2025-06-20 10:54:27,596 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:54:27,596 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:54:27,596 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=1944, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64616), raddr=('127.0.0.1', 5672)>
2025-06-20 10:54:27,597 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 10:54:27,598 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 10:54:27,721 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 10:54:27,721 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 10:54:59,320 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 10:54:59,322 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=1980, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64911), raddr=('127.0.0.1', 5672)>
2025-06-20 10:54:59,322 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECACC26B0>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECACC26B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:54:59,328 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECACC26B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:54:59,328 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECACC26B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:54:59,329 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECACC26B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:54:59,330 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:54:59,336 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:54:59,336 - __main__ - INFO - �����г����������߷���...
2025-06-20 10:54:59,337 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 10:54:59,356 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 10:54:59,357 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 10:54:59,357 - __main__ - INFO - ����������������...
2025-06-20 10:54:59,359 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 10:54:59,359 - producer_health - INFO - ���ö˵�:
2025-06-20 10:54:59,360 - producer_health - INFO -   GET /health - �����������
2025-06-20 10:54:59,360 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 10:54:59,360 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 10:54:59,360 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 10:54:59,361 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 10:54:59,361 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 10:54:59,362 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 10:54:59,362 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 10:54:59,362 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 10:54:59,363 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 10:54:59,364 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 10:54:59,364 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 10:54:59,364 - __main__ - INFO - ��ʼ������������...
2025-06-20 10:54:59,474 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600030.SH
2025-06-20 10:54:59,690 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600061.SH
2025-06-20 10:54:59,838 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600109.SH
2025-06-20 10:55:00,072 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600918.SH
2025-06-20 10:55:00,265 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600958.SH
2025-06-20 10:55:00,485 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600999.SH
2025-06-20 10:55:00,661 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601059.SH
2025-06-20 10:55:00,834 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601066.SH
2025-06-20 10:55:01,000 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601108.SH
2025-06-20 10:55:01,176 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601136.SH
2025-06-20 10:55:01,356 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601211.SH
2025-06-20 10:55:01,538 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601236.SH
2025-06-20 10:55:01,724 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601377.SH
2025-06-20 10:55:01,893 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601456.SH
2025-06-20 10:55:02,110 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601555.SH
2025-06-20 10:55:02,339 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601688.SH
2025-06-20 10:55:02,557 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601788.SH
2025-06-20 10:55:02,776 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601878.SH
2025-06-20 10:55:02,940 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601881.SH
2025-06-20 10:55:03,110 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601901.SH
2025-06-20 10:55:03,175 - __main__ - INFO - ���յ��ź� 2����ʼ���Źر�...
2025-06-20 10:55:03,175 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 10:55:03,175 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 10:55:03,175 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECACC26B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:55:03,177 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECACC26B0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 10:55:03,178 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 10:55:03,180 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=1980, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64911), raddr=('127.0.0.1', 5672)>
2025-06-20 10:55:03,180 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=1980, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64911), raddr=('127.0.0.1', 5672)>
2025-06-20 10:55:03,180 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=1980, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64911), raddr=('127.0.0.1', 5672)>
2025-06-20 10:55:03,181 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:55:03,181 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 10:55:03,182 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=1980, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64911), raddr=('127.0.0.1', 5672)>
2025-06-20 10:55:03,183 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 10:55:03,184 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 10:55:03,225 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 10:55:03,227 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=1716, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64921), raddr=('127.0.0.1', 5672)>
2025-06-20 10:55:03,227 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECAD26770>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECAD26770> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 10:55:03,234 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECAD26770> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:55:03,236 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECAD26770> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:55:03,237 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000001AECAD26770> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 10:55:03,237 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 10:55:03,245 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 10:55:03,633 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 10:55:03,633 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 11:02:09,573 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 11:02:09,575 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 49407), raddr=('127.0.0.1', 5672)>
2025-06-20 11:02:09,576 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A37E710>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A37E710> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 11:02:09,585 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A37E710> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 11:02:09,585 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A37E710> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 11:02:09,586 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A37E710> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 11:02:09,586 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 11:02:09,596 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 11:02:09,596 - __main__ - INFO - �����г����������߷���...
2025-06-20 11:02:09,597 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 11:02:09,624 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 11:02:09,625 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 11:02:09,625 - __main__ - INFO - ����������������...
2025-06-20 11:02:09,627 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 11:02:09,628 - producer_health - INFO - ���ö˵�:
2025-06-20 11:02:09,628 - producer_health - INFO -   GET /health - �����������
2025-06-20 11:02:09,628 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 11:02:09,629 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 11:02:09,629 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 11:02:09,629 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 11:02:09,630 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 11:02:09,630 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 11:02:09,630 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 11:02:09,630 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 11:02:09,630 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 11:02:09,632 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 11:02:09,632 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 11:02:09,632 - __main__ - INFO - ��ʼ������������...
2025-06-20 11:02:09,686 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600030.SH
2025-06-20 11:02:09,837 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600061.SH
2025-06-20 11:02:10,012 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600109.SH
2025-06-20 11:02:10,181 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600918.SH
2025-06-20 11:02:10,354 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600958.SH
2025-06-20 11:02:10,544 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600999.SH
2025-06-20 11:02:10,719 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601059.SH
2025-06-20 11:02:10,896 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601066.SH
2025-06-20 11:02:11,068 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601108.SH
2025-06-20 11:02:11,229 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601136.SH
2025-06-20 11:02:11,447 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601211.SH
2025-06-20 11:02:11,641 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601236.SH
2025-06-20 11:02:11,794 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601377.SH
2025-06-20 11:02:11,944 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601456.SH
2025-06-20 11:02:12,100 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601555.SH
2025-06-20 11:02:12,252 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601688.SH
2025-06-20 11:02:12,561 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601788.SH
2025-06-20 11:02:12,721 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601878.SH
2025-06-20 11:02:12,893 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601881.SH
2025-06-20 11:02:13,263 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601901.SH
2025-06-20 11:02:13,444 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601990.SH
2025-06-20 11:02:13,686 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601995.SH
2025-06-20 11:02:13,859 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000166.SZ
2025-06-20 11:02:14,040 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000776.SZ
2025-06-20 11:02:14,197 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000783.SZ
2025-06-20 11:02:14,354 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002673.SZ
2025-06-20 11:02:14,515 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002736.SZ
2025-06-20 11:02:14,697 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002939.SZ
2025-06-20 11:02:14,866 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002945.SZ
2025-06-20 11:02:15,042 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 300059.SZ
2025-06-20 11:02:15,155 - __main__ - INFO - �������: 30/30 ֻ��Ʊ���ĳɹ�
2025-06-20 11:02:15,155 - __main__ - INFO - ���ݶ��ķ���������
2025-06-20 11:02:15,155 - __main__ - INFO - ��ض˵�:
2025-06-20 11:02:15,156 - __main__ - INFO -   �������: http://localhost:8081/health
2025-06-20 11:02:15,156 - __main__ - INFO -   ϵͳ��Ϣ: http://localhost:8081/system
2025-06-20 11:02:15,156 - __main__ - INFO -   Prometheusָ��: http://localhost:8001/metrics
2025-06-20 11:02:15,156 - __main__ - INFO - �� CTRL+C ֹͣ����
2025-06-20 11:03:13,398 - __main__ - INFO - ���յ��ź� 2����ʼ���Źر�...
2025-06-20 11:03:13,399 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 11:03:13,399 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 11:03:13,400 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A37E710> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 11:03:13,403 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A37E710> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 11:03:13,404 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 11:03:13,408 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 49407), raddr=('127.0.0.1', 5672)>
2025-06-20 11:03:13,412 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 49407), raddr=('127.0.0.1', 5672)>
2025-06-20 11:03:13,414 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 49407), raddr=('127.0.0.1', 5672)>
2025-06-20 11:03:13,415 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 11:03:13,416 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 11:03:13,417 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=2064, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 49407), raddr=('127.0.0.1', 5672)>
2025-06-20 11:03:13,418 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 11:03:13,421 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 11:03:13,515 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 11:03:13,517 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=2484, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 49811), raddr=('127.0.0.1', 5672)>
2025-06-20 11:03:13,519 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A40CD30>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A40CD30> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 11:03:13,534 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A40CD30> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 11:03:13,537 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A40CD30> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 11:03:13,539 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x000002786A40CD30> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 11:03:13,541 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 11:03:13,560 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 11:03:13,746 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 11:03:13,746 - __main__ - INFO - ��Դ������ɣ������ѹر�
2025-06-20 13:47:54,254 - stock_config_loader - INFO - �ɹ����ع�Ʊ�����ļ�: ../config/stocks_config.yaml
2025-06-20 13:47:54,504 - stock_config_loader - INFO - ���ع�Ʊ�� 'default_list': 30 ֻ��Ʊ
2025-06-20 13:47:54,761 - __main__ - INFO - �������ļ������� 30 ֻ��Ʊ
2025-06-20 13:47:54,978 - __main__ - INFO - ��������: period=tick, count=0
2025-06-20 13:47:55,446 - pika.adapters.utils.connection_workflow - INFO - Pika version 1.3.2 connecting to ('127.0.0.1', 5672)
2025-06-20 13:47:55,489 - pika.adapters.utils.io_services_utils - INFO - Socket connected: <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:47:55,501 - pika.adapters.utils.connection_workflow - INFO - Streaming transport linked up: (<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0>, _StreamingProtocolShim: <SelectConnection PROTOCOL transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>).
2025-06-20 13:47:56,449 - pika.adapters.utils.connection_workflow - INFO - AMQPConnector - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:47:56,457 - pika.adapters.utils.connection_workflow - INFO - AMQPConnectionWorkflow - reporting success: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:47:56,466 - pika.adapters.blocking_connection - INFO - Connection workflow succeeded: <SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>
2025-06-20 13:47:56,471 - pika.adapters.blocking_connection - INFO - Created channel=1
2025-06-20 13:47:56,630 - rabbitmq_client - INFO - �ɹ����ӵ�RabbitMQ������
2025-06-20 13:47:56,641 - __main__ - INFO - �����г����������߷���...
2025-06-20 13:47:56,643 - __main__ - INFO - ����Prometheusָ���ռ���...
2025-06-20 13:47:57,042 - producer_metrics - INFO - Producer Prometheusָ����������������˿�: 8001
2025-06-20 13:47:57,113 - producer_metrics - INFO - ϵͳָ���ռ��߳�������
2025-06-20 13:47:57,138 - __main__ - INFO - ����������������...
2025-06-20 13:47:57,216 - producer_health - INFO - Producer������������������: http://0.0.0.0:8081
2025-06-20 13:47:57,256 - producer_health - INFO - ���ö˵�:
2025-06-20 13:47:57,294 - producer_health - INFO -   GET /health - �����������
2025-06-20 13:47:57,322 - producer_health - INFO -   GET /health/live - ����Լ��
2025-06-20 13:47:57,333 - producer_health - INFO -   GET /health/ready - �����Լ��
2025-06-20 13:47:57,339 - producer_health - INFO -   GET /health/detailed - ��ϸ�������
2025-06-20 13:47:57,343 - producer_health - INFO -   GET /stats - ͳ����Ϣ
2025-06-20 13:47:57,352 - producer_health - INFO -   GET /system - ϵͳ��Ϣ
2025-06-20 13:47:57,356 - producer_health - INFO -   GET /metrics - Prometheusָ���ض���
2025-06-20 13:47:57,359 - __main__ - INFO - ��ط����ʼ�����
2025-06-20 13:47:57,363 - __main__ - INFO - Prometheusָ��: http://localhost:8001/metrics
2025-06-20 13:47:57,383 - __main__ - INFO - �������: http://localhost:8081/health
2025-06-20 13:47:57,399 - __main__ - INFO - RabbitMQ���Ӳ��Գɹ�
2025-06-20 13:47:57,403 - __main__ - INFO - ׼������ 30 ֻ��Ʊ
2025-06-20 13:47:57,404 - __main__ - INFO - ��ʼ������������...
2025-06-20 13:47:57,407 - __main__ - INFO - ���Ĳ���: period=tick, count=0, delay=0.1s
2025-06-20 13:47:57,639 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600030.SH
2025-06-20 13:47:57,816 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600061.SH
2025-06-20 13:47:57,976 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600109.SH
2025-06-20 13:47:58,124 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600918.SH
2025-06-20 13:47:58,271 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600958.SH
2025-06-20 13:47:58,430 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 600999.SH
2025-06-20 13:47:58,573 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601059.SH
2025-06-20 13:47:58,775 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601066.SH
2025-06-20 13:47:58,987 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601108.SH
2025-06-20 13:47:59,170 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601136.SH
2025-06-20 13:47:59,325 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601211.SH
2025-06-20 13:47:59,581 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601236.SH
2025-06-20 13:47:59,731 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601377.SH
2025-06-20 13:47:59,873 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601456.SH
2025-06-20 13:48:00,034 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601555.SH
2025-06-20 13:48:00,197 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601688.SH
2025-06-20 13:48:00,345 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601788.SH
2025-06-20 13:48:00,500 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601878.SH
2025-06-20 13:48:00,658 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601881.SH
2025-06-20 13:48:00,853 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601901.SH
2025-06-20 13:48:00,998 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601990.SH
2025-06-20 13:48:01,134 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 601995.SH
2025-06-20 13:48:01,282 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000166.SZ
2025-06-20 13:48:01,425 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000776.SZ
2025-06-20 13:48:01,577 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 000783.SZ
2025-06-20 13:48:01,724 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002673.SZ
2025-06-20 13:48:01,876 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002736.SZ
2025-06-20 13:48:02,032 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002939.SZ
2025-06-20 13:48:02,203 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 002945.SZ
2025-06-20 13:48:02,371 - __main__ - INFO - �ɹ����Ĺ�Ʊ: 300059.SZ
2025-06-20 13:48:02,486 - __main__ - INFO - �������: 30/30 ֻ��Ʊ���ĳɹ�
2025-06-20 13:48:02,486 - __main__ - INFO - ���ݶ��ķ���������
2025-06-20 13:48:02,486 - __main__ - INFO - ��ض˵�:
2025-06-20 13:48:02,486 - __main__ - INFO -   �������: http://localhost:8081/health
2025-06-20 13:48:02,487 - __main__ - INFO -   ϵͳ��Ϣ: http://localhost:8081/system
2025-06-20 13:48:02,487 - __main__ - INFO -   Prometheusָ��: http://localhost:8001/metrics
2025-06-20 13:48:02,488 - __main__ - INFO - �� CTRL+C ֹͣ����
2025-06-20 13:48:32,897 - __main__ - INFO - ���յ��ź� 2����ʼ���Źر�...
2025-06-20 13:48:32,897 - __main__ - INFO - ��ʼ������Դ...
2025-06-20 13:48:32,898 - pika.adapters.blocking_connection - INFO - Closing connection (200): Normal shutdown
2025-06-20 13:48:32,898 - pika.channel - INFO - Closing channel (200): 'Normal shutdown' on <Channel number=1 OPEN conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 13:48:32,901 - pika.channel - INFO - Received <Channel.CloseOk> on <Channel number=1 CLOSING conn=<SelectConnection OPEN transport=<pika.adapters.utils.io_services_utils._AsyncPlaintextTransport object at 0x0000022FEAEC62F0> params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>>
2025-06-20 13:48:32,902 - pika.connection - INFO - Closing connection (200): 'Normal shutdown'
2025-06-20 13:48:32,903 - pika.adapters.utils.io_services_utils - INFO - Aborting transport connection: state=1; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,904 - pika.adapters.utils.io_services_utils - INFO - _AsyncTransportBase._initate_abort(): Initiating abrupt asynchronous transport shutdown: state=1; error=None; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,905 - pika.adapters.utils.io_services_utils - INFO - Deactivating transport: state=1; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,905 - pika.connection - INFO - AMQP stack terminated, failed to connect, or aborted: opened=True, error-arg=None; pending-error=ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 13:48:32,907 - pika.connection - INFO - Stack terminated due to ConnectionClosedByClient: (200) 'Normal shutdown'
2025-06-20 13:48:32,907 - pika.adapters.utils.io_services_utils - INFO - Closing transport socket and unlinking: state=3; <socket.socket fd=3564, family=AddressFamily.AF_INET, type=SocketKind.SOCK_STREAM, proto=6, laddr=('127.0.0.1', 64482), raddr=('127.0.0.1', 5672)>
2025-06-20 13:48:32,908 - pika.adapters.blocking_connection - INFO - User-initiated close: result=BlockingConnection__OnClosedArgs(connection=<SelectConnection CLOSED transport=None params=<ConnectionParameters host=127.0.0.1 port=5672 virtual_host=/ ssl=False>>, error=ConnectionClosedByClient: (200) 'Normal shutdown')
2025-06-20 13:48:32,909 - rabbitmq_client - INFO - �ѶϿ�RabbitMQ����
2025-06-20 13:48:32,952 - producer_health - INFO - Producer��������������ֹͣ
2025-06-20 13:48:32,952 - __main__ - INFO - ��Դ������ɣ������ѹر�
