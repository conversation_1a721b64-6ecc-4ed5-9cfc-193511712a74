#!/usr/bin/env python3
"""
Producer服务系统测试脚本
用于测试Producer的监控和健康检查功能
"""

import requests
import time
import json


def test_producer_health():
    """测试Producer健康状态"""
    print("🧪 测试Producer健康状态...")
    
    try:
        response = requests.get('http://localhost:8081/health', timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Producer健康状态: {health_data['status']}")
            print(f"   服务: {health_data.get('service', 'unknown')}")
            print(f"   版本: {health_data.get('version', 'unknown')}")
            return True
        else:
            print(f"❌ 健康检查失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Producer健康检查接口: {e}")
        return False


def test_producer_detailed_health():
    """测试Producer详细健康检查"""
    print("🧪 测试Producer详细健康检查...")
    
    try:
        response = requests.get('http://localhost:8081/health/detailed', timeout=5)
        
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Producer详细健康状态: {health_data['status']}")
            
            # 显示系统信息
            if 'components' in health_data and 'system' in health_data['components']:
                system = health_data['components']['system']
                if 'cpu' in system:
                    print(f"   CPU使用率: {system['cpu'].get('usage_percent', 'N/A')}%")
                if 'memory' in system:
                    print(f"   内存使用率: {system['memory'].get('usage_percent', 'N/A')}%")
                    print(f"   可用内存: {system['memory'].get('available_gb', 'N/A')} GB")
            
            return True
        else:
            print(f"❌ 详细健康检查失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Producer详细健康检查接口: {e}")
        return False


def test_producer_system_info():
    """测试Producer系统信息"""
    print("🧪 测试Producer系统信息...")
    
    try:
        response = requests.get('http://localhost:8081/system', timeout=5)
        
        if response.status_code == 200:
            system_data = response.json()
            print("✅ Producer系统信息获取成功")
            print(f"   平台: {system_data.get('platform', 'unknown')}")
            print(f"   Python版本: {system_data.get('python_version', 'unknown')}")
            print(f"   CPU核心数: {system_data.get('cpu_count', 'unknown')}")
            return True
        else:
            print(f"❌ 获取系统信息失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Producer系统信息接口: {e}")
        return False


def test_producer_prometheus_metrics():
    """测试Producer Prometheus指标"""
    print("🧪 测试Producer Prometheus指标...")
    
    try:
        response = requests.get('http://localhost:8001/metrics', timeout=5)
        
        if response.status_code == 200:
            metrics_text = response.text
            
            # 检查关键指标是否存在
            key_metrics = [
                'producer_service_uptime_seconds',
                'producer_service_status',
                'producer_cpu_usage_percent',
                'producer_memory_usage_percent',
                'producer_messages_sent_total',
                'producer_data_callbacks_total'
            ]
            
            missing_metrics = []
            found_metrics = []
            
            for metric in key_metrics:
                if metric in metrics_text:
                    found_metrics.append(metric)
                else:
                    missing_metrics.append(metric)
            
            print(f"✅ Producer Prometheus指标检查完成")
            print(f"   找到指标: {len(found_metrics)}/{len(key_metrics)}")
            
            if found_metrics:
                print("   已找到的指标:")
                for metric in found_metrics[:5]:  # 只显示前5个
                    print(f"     - {metric}")
            
            if missing_metrics:
                print("   缺少的指标:")
                for metric in missing_metrics:
                    print(f"     - {metric}")
                return False
            
            return True
        else:
            print(f"❌ 获取Prometheus指标失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Producer Prometheus指标接口: {e}")
        return False


def test_producer_stats():
    """测试Producer统计信息"""
    print("🧪 测试Producer统计信息...")
    
    try:
        response = requests.get('http://localhost:8081/stats', timeout=5)
        
        if response.status_code == 200:
            stats_data = response.json()
            print("✅ Producer统计信息获取成功")
            
            if 'service_stats' in stats_data:
                service_stats = stats_data['service_stats']
                print(f"   运行时间: {service_stats.get('uptime_seconds', 0):.1f} 秒")
                print(f"   活跃线程: {service_stats.get('active_threads', 0)}")
            
            if 'metrics_summary' in stats_data:
                metrics_summary = stats_data['metrics_summary']
                print(f"   CPU使用率: {metrics_summary.get('cpu_percent', 'N/A')}%")
                print(f"   内存使用率: {metrics_summary.get('memory_percent', 'N/A')}%")
            
            return True
        else:
            print(f"❌ 获取统计信息失败，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Producer统计信息接口: {e}")
        return False


def test_producer_readiness():
    """测试Producer就绪性检查"""
    print("🧪 测试Producer就绪性检查...")
    
    try:
        response = requests.get('http://localhost:8081/health/ready', timeout=5)
        
        if response.status_code == 200:
            ready_data = response.json()
            print(f"✅ Producer就绪状态: {ready_data['status']}")
            
            if 'checks' in ready_data:
                checks = ready_data['checks']
                print(f"   CPU检查: {checks.get('cpu', 'unknown')}")
                print(f"   内存检查: {checks.get('memory', 'unknown')}")
            
            return True
        else:
            print(f"⚠️  Producer未就绪，状态码: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Producer就绪性检查接口: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始Producer服务监控测试")
    print("=" * 50)
    
    tests = [
        ("基本健康检查", test_producer_health),
        ("详细健康检查", test_producer_detailed_health),
        ("系统信息", test_producer_system_info),
        ("Prometheus指标", test_producer_prometheus_metrics),
        ("统计信息", test_producer_stats),
        ("就绪性检查", test_producer_readiness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
        
        time.sleep(1)  # 短暂延迟
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！Producer监控功能正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查Producer服务状态")
        return 1


if __name__ == '__main__':
    exit(main())
