"""
健康检查HTTP服务模块
提供服务健康状态检查接口
"""

import json
import logging
import threading
import time
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from typing import Dict, Any
from urllib.parse import urlparse, parse_qs

from rabbitmq_consumer import get_consumer
from database_manager import get_database_manager
from metrics import get_metrics_collector


class HealthCheckHandler(BaseHTTPRequestHandler):
    """健康检查HTTP请求处理器"""
    
    def __init__(self, *args, **kwargs):
        self.logger = logging.getLogger(__name__)
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        try:
            parsed_path = urlparse(self.path)
            path = parsed_path.path
            query_params = parse_qs(parsed_path.query)
            
            if path == '/health':
                self._handle_health_check(query_params)
            elif path == '/health/live':
                self._handle_liveness_check()
            elif path == '/health/ready':
                self._handle_readiness_check()
            elif path == '/health/detailed':
                self._handle_detailed_health_check()
            elif path == '/stats':
                self._handle_stats()
            elif path == '/metrics':
                self._handle_metrics_redirect()
            else:
                self._send_response(404, {'error': 'Not Found', 'path': path})
                
        except Exception as e:
            self.logger.error(f"处理健康检查请求时发生错误: {e}")
            self._send_response(500, {'error': 'Internal Server Error', 'message': str(e)})
    
    def _handle_health_check(self, query_params: Dict):
        """处理基本健康检查"""
        try:
            # 获取详细程度参数
            detail_level = query_params.get('detail', ['basic'])[0]
            
            if detail_level == 'detailed':
                response = self._get_detailed_health()
            else:
                response = self._get_basic_health()
            
            status_code = 200 if response['status'] == 'healthy' else 503
            self._send_response(status_code, response)
            
        except Exception as e:
            self.logger.error(f"健康检查失败: {e}")
            self._send_response(500, {'status': 'error', 'message': str(e)})
    
    def _handle_liveness_check(self):
        """处理存活性检查"""
        # 简单的存活性检查 - 服务是否在运行
        response = {
            'status': 'alive',
            'timestamp': datetime.now().isoformat(),
            'uptime_seconds': time.time() - get_metrics_collector().start_time
        }
        self._send_response(200, response)
    
    def _handle_readiness_check(self):
        """处理就绪性检查"""
        try:
            # 检查关键组件是否就绪
            consumer = get_consumer()
            db_manager = get_database_manager()
            
            # 检查RabbitMQ连接
            rabbitmq_ready = consumer.connection and not consumer.connection.is_closed
            
            # 检查数据库连接
            db_health = db_manager.health_check()
            db_ready = db_health['status'] == 'healthy'
            
            # 整体就绪状态
            ready = rabbitmq_ready and db_ready
            
            response = {
                'status': 'ready' if ready else 'not_ready',
                'timestamp': datetime.now().isoformat(),
                'components': {
                    'rabbitmq': 'ready' if rabbitmq_ready else 'not_ready',
                    'database': 'ready' if db_ready else 'not_ready'
                }
            }
            
            status_code = 200 if ready else 503
            self._send_response(status_code, response)
            
        except Exception as e:
            self.logger.error(f"就绪性检查失败: {e}")
            self._send_response(503, {'status': 'not_ready', 'error': str(e)})
    
    def _handle_detailed_health_check(self):
        """处理详细健康检查"""
        response = self._get_detailed_health()
        status_code = 200 if response['status'] == 'healthy' else 503
        self._send_response(status_code, response)
    
    def _handle_stats(self):
        """处理统计信息请求"""
        try:
            consumer = get_consumer()
            metrics_collector = get_metrics_collector()
            
            stats = {
                'consumer_stats': consumer.get_stats(),
                'metrics_summary': metrics_collector.get_metrics_summary(),
                'timestamp': datetime.now().isoformat()
            }
            
            self._send_response(200, stats)
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            self._send_response(500, {'error': str(e)})
    
    def _handle_metrics_redirect(self):
        """处理Prometheus指标重定向"""
        metrics_port = get_metrics_collector().port
        self._send_redirect(f"http://localhost:{metrics_port}/metrics")
    
    def _get_basic_health(self) -> Dict[str, Any]:
        """获取基本健康状态"""
        try:
            consumer = get_consumer()
            consumer_health = consumer.health_check()
            
            overall_status = 'healthy' if consumer_health['status'] == 'healthy' else 'unhealthy'
            
            return {
                'status': overall_status,
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'service': 'market_data_consumer'
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _get_detailed_health(self) -> Dict[str, Any]:
        """获取详细健康状态"""
        try:
            consumer = get_consumer()
            db_manager = get_database_manager()
            metrics_collector = get_metrics_collector()
            
            # 获取各组件健康状态
            consumer_health = consumer.health_check()
            db_health = db_manager.health_check()
            
            # 计算整体状态
            components_healthy = (
                consumer_health['status'] == 'healthy' and
                db_health['status'] == 'healthy'
            )
            
            overall_status = 'healthy' if components_healthy else 'unhealthy'
            
            return {
                'status': overall_status,
                'timestamp': datetime.now().isoformat(),
                'version': '1.0.0',
                'service': 'market_data_consumer',
                'uptime_seconds': time.time() - metrics_collector.start_time,
                'components': {
                    'consumer': consumer_health,
                    'database': db_health,
                    'metrics': {
                        'status': 'healthy',
                        'port': metrics_collector.port
                    }
                },
                'stats': consumer.get_stats()
            }
            
        except Exception as e:
            return {
                'status': 'unhealthy',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    def _send_response(self, status_code: int, data: Dict[str, Any]):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(data, indent=2, ensure_ascii=False)
        self.wfile.write(response_json.encode('utf-8'))
    
    def _send_redirect(self, location: str):
        """发送重定向响应"""
        self.send_response(302)
        self.send_header('Location', location)
        self.end_headers()
    
    def log_message(self, format, *args):
        """重写日志方法以使用我们的logger"""
        self.logger.info(f"{self.address_string()} - {format % args}")


class HealthCheckServer:
    """健康检查HTTP服务器"""
    
    def __init__(self, host: str = '0.0.0.0', port: int = 8080):
        """
        初始化健康检查服务器
        
        Args:
            host: 监听主机
            port: 监听端口
        """
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.logger = logging.getLogger(__name__)
        
    def start(self):
        """启动健康检查服务器"""
        try:
            self.server = HTTPServer((self.host, self.port), HealthCheckHandler)
            self.server_thread = threading.Thread(target=self.server.serve_forever, daemon=True)
            self.server_thread.start()
            
            self.logger.info(f"健康检查服务器已启动: http://{self.host}:{self.port}")
            self.logger.info("可用端点:")
            self.logger.info("  GET /health - 基本健康检查")
            self.logger.info("  GET /health/live - 存活性检查")
            self.logger.info("  GET /health/ready - 就绪性检查")
            self.logger.info("  GET /health/detailed - 详细健康检查")
            self.logger.info("  GET /stats - 统计信息")
            self.logger.info("  GET /metrics - Prometheus指标重定向")
            
        except Exception as e:
            self.logger.error(f"启动健康检查服务器失败: {e}")
            raise
    
    def stop(self):
        """停止健康检查服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            self.logger.info("健康检查服务器已停止")
    
    def is_running(self) -> bool:
        """检查服务器是否在运行"""
        return self.server_thread and self.server_thread.is_alive()


# 全局健康检查服务器实例
_health_server = None


def start_health_server(host: str = '0.0.0.0', port: int = 8080) -> HealthCheckServer:
    """启动健康检查服务器"""
    global _health_server
    if _health_server is None:
        _health_server = HealthCheckServer(host, port)
        _health_server.start()
    return _health_server


def stop_health_server():
    """停止健康检查服务器"""
    global _health_server
    if _health_server:
        _health_server.stop()
        _health_server = None


def get_health_server() -> HealthCheckServer:
    """获取健康检查服务器实例"""
    global _health_server
    return _health_server
