from xtquant import xtdata
import time
import json
import pandas as pd
import signal
import sys
import os
from rabbitmq_client import get_rabbitmq_client

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from producer_metrics import (
    get_producer_metrics_collector,
    set_service_status,
    set_qmt_connection_status,
    set_rabbitmq_connection_status,
    record_message_sent,
    record_data_callback,
    set_subscribed_stocks_count,
    record_error
)
from producer_health import start_producer_health_server, stop_producer_health_server
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('../logs/producer.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)
  

code = ['600030.SH', '600061.SH', '600109.SH', '600918.SH', '600958.SH', '600999.SH', '601059.SH', '601066.SH', '601108.SH', '601136.SH', '601211.SH', '601236.SH', '601377.SH', '601456.SH', '601555.SH', '601688.SH', '601788.SH', '601878.SH', '601881.SH', '601901.SH', '601990.SH', '601995.SH', '000166.SZ', '000776.SZ', '000783.SZ', '002673.SZ', '002736.SZ', '002939.SZ', '002945.SZ', '300059.SZ']

 

# 初始化监控和健康检查
def initialize_monitoring():
    """初始化监控和健康检查服务"""
    try:
        # 启动Prometheus指标收集器
        logger.info("启动Prometheus指标收集器...")
        metrics_collector = get_producer_metrics_collector(port=8001)

        # 启动健康检查服务器
        logger.info("启动健康检查服务器...")
        health_server = start_producer_health_server(host='0.0.0.0', port=8081)

        # 设置服务状态
        set_service_status(True)

        logger.info("监控服务初始化完成")
        logger.info("Prometheus指标: http://localhost:8001/metrics")
        logger.info("健康检查: http://localhost:8081/health")

        return metrics_collector, health_server

    except Exception as e:
        logger.error(f"初始化监控服务失败: {e}")
        raise

# 获取RabbitMQ客户端
rabbitmq_client = get_rabbitmq_client()

#订阅最新行情
def callback_func(data):
    """
    行情数据回调函数
    将接收到的数据发送到RabbitMQ队列
    """
    start_time = time.time()

    try:
        logger.debug(f'回调触发: {len(str(data))} bytes')

        # 记录数据回调
        record_data_callback()

        # 发送数据到RabbitMQ
        success = rabbitmq_client.send_message(data)

        # 记录发送结果
        send_duration = time.time() - start_time
        record_message_sent(success, send_duration)

        if success:
            logger.debug(f"数据已发送到RabbitMQ: {len(str(data))} bytes, 耗时: {send_duration:.3f}s")
        else:
            logger.error("发送数据到RabbitMQ失败")
            record_error("rabbitmq_send_failed")

    except Exception as e:
        logger.error(f"callback_func处理数据时发生错误: {e}")
        record_error("callback_processing_error")

        # 记录失败的发送
        send_duration = time.time() - start_time
        record_message_sent(False, send_duration)
    

def setup_signal_handlers():
    """设置信号处理器"""
    def signal_handler(signum, frame):
        logger.info(f"接收到信号 {signum}，开始优雅关闭...")
        cleanup_and_exit()

    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

def cleanup_and_exit():
    """清理资源并退出"""
    try:
        logger.info("开始清理资源...")

        # 设置服务状态为停止
        set_service_status(False)

        # 清理RabbitMQ客户端
        from rabbitmq_client import close_rabbitmq_client
        close_rabbitmq_client()

        # 停止健康检查服务器
        stop_producer_health_server()

        logger.info("资源清理完成，服务已关闭")

    except Exception as e:
        logger.error(f"清理资源时发生错误: {e}")
    finally:
        sys.exit(0)

def main():
    """主函数"""
    try:
        logger.info("启动市场数据生产者服务...")

        # 初始化监控
        metrics_collector, health_server = initialize_monitoring()

        # 设置信号处理器
        setup_signal_handlers()

        # 检查RabbitMQ连接
        try:
            # 测试RabbitMQ连接
            test_success = rabbitmq_client.send_message({"test": "connection"})
            set_rabbitmq_connection_status(test_success)

            if test_success:
                logger.info("RabbitMQ连接测试成功")
            else:
                logger.warning("RabbitMQ连接测试失败，但继续运行")

        except Exception as e:
            logger.error(f"RabbitMQ连接测试失败: {e}")
            set_rabbitmq_connection_status(False)
            record_error("rabbitmq_connection_failed")

        # 设置订阅股票数量
        set_subscribed_stocks_count(len(code))
        logger.info(f"准备订阅 {len(code)} 只股票")

        # 订阅行情数据
        logger.info("开始订阅行情数据...")
        xtdata.subscribe_quote(code, period='1m', count=0, callback=callback_func)

        # 设置QMT连接状态（假设订阅成功表示连接正常）
        set_qmt_connection_status(True)

        logger.info("数据订阅服务已启动")
        logger.info("监控端点:")
        logger.info("  健康检查: http://localhost:8081/health")
        logger.info("  系统信息: http://localhost:8081/system")
        logger.info("  Prometheus指标: http://localhost:8001/metrics")
        logger.info("按 CTRL+C 停止服务")

        # 死循环 阻塞主线程退出
        xtdata.run()

    except KeyboardInterrupt:
        logger.info("接收到键盘中断信号")
        cleanup_and_exit()
    except Exception as e:
        logger.error(f"运行过程中发生错误: {e}")
        record_error("service_runtime_error")
        set_qmt_connection_status(False)
        cleanup_and_exit()

if __name__ == '__main__':
    main()