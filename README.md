# QMT数据收集与RabbitMQ集成

本项目实现了将QMT（迅投量化交易终端）的市场数据通过RabbitMQ进行消息队列处理。

## 功能特性

- ✅ 实时订阅股票行情数据
- ✅ 将数据发送到RabbitMQ队列
- ✅ 支持配置文件管理
- ✅ 完整的单元测试覆盖
- ✅ 错误处理和重连机制
- ✅ 日志记录功能

## 项目结构

```
qmt_data_collect/
├── config.yaml              # RabbitMQ配置文件
├── rabbitmq_client.py       # RabbitMQ客户端实现
├── test.py                  # 主程序（集成RabbitMQ）
├── test_rabbitmq_client.py  # 单元测试
├── run_tests.py             # 测试运行脚本
├── requirements.txt         # 依赖包列表
└── README.md               # 说明文档
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置说明

### RabbitMQ配置 (config.yaml)

```yaml
rabbitmq:
  host: "localhost"           # RabbitMQ服务器地址
  port: 5672                 # 端口
  username: "guest"          # 用户名
  password: "guest"          # 密码
  virtual_host: "/"          # 虚拟主机
  
  queue:
    name: "market_data_queue"  # 队列名称
    durable: true             # 队列持久化
    
  exchange:
    name: "market_data_exchange"  # 交换机名称
    type: "direct"               # 交换机类型
    
  routing_key: "market.data"     # 路由键
```

## 使用方法

### 1. 启动RabbitMQ服务

确保RabbitMQ服务正在运行：

```bash
# Ubuntu/Debian
sudo systemctl start rabbitmq-server

# Windows (如果使用Docker)
docker run -d --name rabbitmq -p 5672:5672 -p 15672:15672 rabbitmq:3-management
```

### 2. 运行数据收集程序

```bash
python test.py
```

程序将：
- 连接到RabbitMQ服务器
- 订阅指定股票的实时行情
- 将接收到的数据发送到RabbitMQ队列

### 3. 运行测试

```bash
# 运行所有测试
python run_tests.py

# 或者使用pytest直接运行
pytest test_rabbitmq_client.py -v
```

## 代码示例

### 基本使用

```python
from rabbitmq_client import RabbitMQClient

# 创建客户端
client = RabbitMQClient("config.yaml")

# 连接到RabbitMQ
if client.connect():
    # 发送消息
    data = {"symbol": "000001.SZ", "price": 11.75}
    client.send_message(data)
    
    # 断开连接
    client.disconnect()
```

### 使用上下文管理器

```python
from rabbitmq_client import RabbitMQClient

with RabbitMQClient("config.yaml") as client:
    data = {"symbol": "000001.SZ", "price": 11.75}
    client.send_message(data)
```

### 批量发送

```python
from rabbitmq_client import RabbitMQClient

client = RabbitMQClient("config.yaml")
client.connect()

data_list = [
    {"symbol": "000001.SZ", "price": 11.75},
    {"symbol": "000002.SZ", "price": 25.30}
]

success_count = client.send_batch_messages(data_list)
print(f"成功发送 {success_count} 条消息")
```

## 消息格式

发送到RabbitMQ的消息格式：

```json
{
    "timestamp": "2025-01-01T10:30:00.123456",
    "data": {
        "symbol": "000001.SZ",
        "time": 1640995200000,
        "open": 11.73,
        "high": 11.77,
        "low": 11.70,
        "close": 11.75,
        "volume": 1000000,
        "amount": 11750000.0,
        "settelementPrice": 0.0,
        "openInterest": 14,
        "preClose": 11.70,
        "suspendFlag": 0
    }
}
```

## 错误处理

程序包含完善的错误处理机制：

- **连接失败**: 自动重试连接
- **发送失败**: 记录错误日志
- **配置错误**: 详细的错误提示
- **网络中断**: 自动重连机制

## 日志配置

日志文件位置：`logs/rabbitmq_client.log`

可以通过配置文件调整日志级别：

```yaml
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/rabbitmq_client.log"
```

## 性能优化

- 使用连接池减少连接开销
- 批量发送消息提高吞吐量
- 消息持久化保证数据安全
- 异步处理避免阻塞

## 故障排除

### 常见问题

1. **连接被拒绝**
   - 检查RabbitMQ服务是否运行
   - 验证用户名和密码
   - 确认端口是否正确

2. **队列不存在**
   - 程序会自动创建队列
   - 检查权限设置

3. **消息发送失败**
   - 查看日志文件
   - 检查网络连接
   - 验证配置文件

## 开发和测试

### 运行测试

```bash
# 安装依赖并运行测试
python run_tests.py --install

# 仅运行测试
python run_tests.py

# 运行特定测试
pytest test_rabbitmq_client.py::TestRabbitMQClient::test_connect_success -v
```

### 代码覆盖率

```bash
pytest test_rabbitmq_client.py --cov=rabbitmq_client --cov-report=html
```

## 许可证

MIT License
