"""
Prometheus监控指标模块
用于收集和暴露应用程序指标
"""

import time
import threading
from typing import Dict, Any
from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
import logging


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, port: int = 8000):
        """
        初始化指标收集器
        
        Args:
            port: Prometheus指标暴露端口
        """
        self.port = port
        self.logger = logging.getLogger(__name__)
        
        # 定义指标
        self._init_metrics()
        
        # 启动HTTP服务器
        self._start_metrics_server()
        
    def _init_metrics(self):
        """初始化Prometheus指标"""
        
        # 应用信息
        self.app_info = Info('market_data_consumer_info', 'Market data consumer application info')
        self.app_info.info({
            'version': '1.0.0',
            'component': 'market_data_consumer'
        })
        
        # 消息处理指标
        self.messages_received_total = Counter(
            'messages_received_total',
            'Total number of messages received from RabbitMQ',
            ['queue', 'status']
        )
        
        self.messages_processed_total = Counter(
            'messages_processed_total',
            'Total number of messages processed successfully',
            ['symbol']
        )
        
        self.messages_failed_total = Counter(
            'messages_failed_total',
            'Total number of messages failed to process',
            ['error_type', 'symbol']
        )
        
        self.message_processing_duration = Histogram(
            'message_processing_duration_seconds',
            'Time spent processing messages',
            ['symbol'],
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        )
        
        # 数据库指标
        self.database_operations_total = Counter(
            'database_operations_total',
            'Total number of database operations',
            ['operation', 'status']
        )
        
        self.database_connection_pool_size = Gauge(
            'database_connection_pool_size',
            'Current size of database connection pool'
        )
        
        self.database_query_duration = Histogram(
            'database_query_duration_seconds',
            'Time spent executing database queries',
            ['operation'],
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
        )
        
        # RabbitMQ指标
        self.rabbitmq_connection_status = Gauge(
            'rabbitmq_connection_status',
            'RabbitMQ connection status (1=connected, 0=disconnected)'
        )
        
        self.rabbitmq_consumer_status = Gauge(
            'rabbitmq_consumer_status',
            'RabbitMQ consumer status (1=consuming, 0=stopped)'
        )
        
        # 系统指标
        self.system_uptime_seconds = Gauge(
            'system_uptime_seconds',
            'System uptime in seconds'
        )
        
        self.active_threads = Gauge(
            'active_threads',
            'Number of active threads'
        )
        
        # 业务指标
        self.stock_quotes_stored_total = Counter(
            'stock_quotes_stored_total',
            'Total number of stock quotes stored in database',
            ['symbol', 'market']
        )
        
        self.latest_quote_timestamp = Gauge(
            'latest_quote_timestamp',
            'Timestamp of the latest quote received',
            ['symbol']
        )
        
        self.data_lag_seconds = Gauge(
            'data_lag_seconds',
            'Data lag in seconds (current time - latest quote time)',
            ['symbol']
        )
        
        # 记录启动时间
        self.start_time = time.time()
        
    def _start_metrics_server(self):
        """启动Prometheus指标HTTP服务器"""
        try:
            start_http_server(self.port)
            self.logger.info(f"Prometheus指标服务器已启动，端口: {self.port}")
        except Exception as e:
            self.logger.error(f"启动Prometheus指标服务器失败: {e}")
            raise
    
    def record_message_received(self, queue: str, status: str = 'success'):
        """记录接收到的消息"""
        self.messages_received_total.labels(queue=queue, status=status).inc()
    
    def record_message_processed(self, symbol: str, processing_time: float):
        """记录处理成功的消息"""
        self.messages_processed_total.labels(symbol=symbol).inc()
        self.message_processing_duration.labels(symbol=symbol).observe(processing_time)
    
    def record_message_failed(self, error_type: str, symbol: str = 'unknown'):
        """记录处理失败的消息"""
        self.messages_failed_total.labels(error_type=error_type, symbol=symbol).inc()
    
    def record_database_operation(self, operation: str, status: str, duration: float):
        """记录数据库操作"""
        self.database_operations_total.labels(operation=operation, status=status).inc()
        self.database_query_duration.labels(operation=operation).observe(duration)
    
    def update_database_pool_size(self, size: int):
        """更新数据库连接池大小"""
        self.database_connection_pool_size.set(size)
    
    def update_rabbitmq_connection_status(self, connected: bool):
        """更新RabbitMQ连接状态"""
        self.rabbitmq_connection_status.set(1 if connected else 0)
    
    def update_rabbitmq_consumer_status(self, consuming: bool):
        """更新RabbitMQ消费者状态"""
        self.rabbitmq_consumer_status.set(1 if consuming else 0)
    
    def record_stock_quote_stored(self, symbol: str, market: str, timestamp: float):
        """记录存储的股票行情"""
        self.stock_quotes_stored_total.labels(symbol=symbol, market=market).inc()
        self.latest_quote_timestamp.labels(symbol=symbol).set(timestamp)
        
        # 计算数据延迟
        current_time = time.time()
        lag = current_time - (timestamp / 1000)  # 假设timestamp是毫秒
        self.data_lag_seconds.labels(symbol=symbol).set(lag)
    
    def update_system_metrics(self):
        """更新系统指标"""
        # 更新运行时间
        uptime = time.time() - self.start_time
        self.system_uptime_seconds.set(uptime)
        
        # 更新活跃线程数
        active_count = threading.active_count()
        self.active_threads.set(active_count)
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        return {
            'uptime_seconds': time.time() - self.start_time,
            'active_threads': threading.active_count(),
            'metrics_port': self.port
        }


# 全局指标收集器实例
_metrics_collector = None


def get_metrics_collector(port: int = 8000) -> MetricsCollector:
    """获取指标收集器单例"""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector(port)
    return _metrics_collector


def record_message_received(queue: str, status: str = 'success'):
    """记录接收到的消息"""
    collector = get_metrics_collector()
    collector.record_message_received(queue, status)


def record_message_processed(symbol: str, processing_time: float):
    """记录处理成功的消息"""
    collector = get_metrics_collector()
    collector.record_message_processed(symbol, processing_time)


def record_message_failed(error_type: str, symbol: str = 'unknown'):
    """记录处理失败的消息"""
    collector = get_metrics_collector()
    collector.record_message_failed(error_type, symbol)


def record_database_operation(operation: str, status: str, duration: float):
    """记录数据库操作"""
    collector = get_metrics_collector()
    collector.record_database_operation(operation, status, duration)


def update_database_pool_size(size: int):
    """更新数据库连接池大小"""
    collector = get_metrics_collector()
    collector.update_database_pool_size(size)


def update_rabbitmq_connection_status(connected: bool):
    """更新RabbitMQ连接状态"""
    collector = get_metrics_collector()
    collector.update_rabbitmq_connection_status(connected)


def update_rabbitmq_consumer_status(consuming: bool):
    """更新RabbitMQ消费者状态"""
    collector = get_metrics_collector()
    collector.update_rabbitmq_consumer_status(consuming)


def record_stock_quote_stored(symbol: str, market: str, timestamp: float):
    """记录存储的股票行情"""
    collector = get_metrics_collector()
    collector.record_stock_quote_stored(symbol, market, timestamp)


def update_system_metrics():
    """更新系统指标"""
    collector = get_metrics_collector()
    collector.update_system_metrics()
