#!/usr/bin/env python3
"""
市场数据消费者主服务程序
消费RabbitMQ消息并存储到MySQL数据库
集成Prometheus监控和健康检查
"""

import argparse
import logging
import signal
import sys
import time
import threading
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

from rabbitmq_consumer import get_consumer, close_consumer
from database_manager import get_database_manager, close_database_manager
from metrics import get_metrics_collector, update_system_metrics
from health_check import start_health_server, stop_health_server


class MarketDataConsumerService:
    """市场数据消费者服务"""
    
    def __init__(self, config_path: str = "config.yaml", 
                 db_config_path: str = "config/database.yaml",
                 metrics_port: int = 8000,
                 health_port: int = 8080):
        """
        初始化服务
        
        Args:
            config_path: RabbitMQ配置文件路径
            db_config_path: 数据库配置文件路径
            metrics_port: Prometheus指标端口
            health_port: 健康检查端口
        """
        self.config_path = config_path
        self.db_config_path = db_config_path
        self.metrics_port = metrics_port
        self.health_port = health_port
        
        self.logger = self._setup_logger()
        self.running = False
        self.shutdown_event = threading.Event()
        
        # 组件实例
        self.consumer = None
        self.db_manager = None
        self.metrics_collector = None
        self.health_server = None
        
        # 系统指标更新线程
        self.metrics_thread = None
        
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/consumer_main.log'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        return logging.getLogger(__name__)
    
    def _setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            self.logger.info(f"接收到信号 {signum}，开始优雅关闭...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _start_metrics_updater(self):
        """启动系统指标更新线程"""
        def update_metrics():
            while not self.shutdown_event.is_set():
                try:
                    update_system_metrics()
                    
                    # 更新数据库连接池大小
                    if self.db_manager:
                        pool_size = self.db_manager._connection_pool.qsize()
                        self.metrics_collector.update_database_pool_size(pool_size)
                    
                    # 更新RabbitMQ状态
                    if self.consumer:
                        connected = self.consumer.connection and not self.consumer.connection.is_closed
                        self.metrics_collector.update_rabbitmq_connection_status(connected)
                        self.metrics_collector.update_rabbitmq_consumer_status(self.consumer.consuming)
                    
                except Exception as e:
                    self.logger.error(f"更新系统指标时发生错误: {e}")
                
                # 每30秒更新一次
                self.shutdown_event.wait(30)
        
        self.metrics_thread = threading.Thread(target=update_metrics, daemon=True)
        self.metrics_thread.start()
        self.logger.info("系统指标更新线程已启动")
    
    def initialize(self) -> bool:
        """初始化所有组件"""
        try:
            self.logger.info("正在初始化市场数据消费者服务...")
            
            # 初始化Prometheus指标收集器
            self.logger.info(f"启动Prometheus指标服务器，端口: {self.metrics_port}")
            self.metrics_collector = get_metrics_collector(self.metrics_port)
            
            # 初始化数据库管理器
            self.logger.info("初始化数据库管理器...")
            self.db_manager = get_database_manager(self.db_config_path)
            
            # 测试数据库连接
            db_health = self.db_manager.health_check()
            if db_health['status'] != 'healthy':
                self.logger.error(f"数据库连接不健康: {db_health}")
                return False
            
            self.logger.info("数据库连接正常")
            
            # 初始化RabbitMQ消费者
            self.logger.info("初始化RabbitMQ消费者...")
            self.consumer = get_consumer(self.config_path, self.db_config_path)
            
            if not self.consumer.connect():
                self.logger.error("无法连接到RabbitMQ服务器")
                return False
            
            self.logger.info("RabbitMQ连接正常")
            
            # 启动健康检查服务器
            self.logger.info(f"启动健康检查服务器，端口: {self.health_port}")
            self.health_server = start_health_server('0.0.0.0', self.health_port)
            
            # 启动系统指标更新线程
            self._start_metrics_updater()
            
            self.logger.info("所有组件初始化完成")
            return True
            
        except Exception as e:
            self.logger.error(f"初始化失败: {e}")
            return False
    
    def start(self):
        """启动服务"""
        if not self.initialize():
            self.logger.error("服务初始化失败，退出")
            sys.exit(1)
        
        # 设置信号处理器
        self._setup_signal_handlers()
        
        try:
            self.running = True
            self.logger.info("市场数据消费者服务已启动")
            self.logger.info(f"健康检查: http://localhost:{self.health_port}/health")
            self.logger.info(f"Prometheus指标: http://localhost:{self.metrics_port}/metrics")
            
            # 开始消费消息
            self.consumer.start_consuming()
            
        except KeyboardInterrupt:
            self.logger.info("接收到键盘中断信号")
        except Exception as e:
            self.logger.error(f"服务运行时发生错误: {e}")
        finally:
            self.shutdown()
    
    def shutdown(self):
        """优雅关闭服务"""
        if not self.running:
            return
        
        self.logger.info("开始关闭服务...")
        self.running = False
        self.shutdown_event.set()
        
        try:
            # 停止消费者
            if self.consumer:
                self.logger.info("停止RabbitMQ消费者...")
                close_consumer()
            
            # 关闭数据库连接
            if self.db_manager:
                self.logger.info("关闭数据库连接...")
                close_database_manager()
            
            # 停止健康检查服务器
            if self.health_server:
                self.logger.info("停止健康检查服务器...")
                stop_health_server()
            
            # 等待指标更新线程结束
            if self.metrics_thread and self.metrics_thread.is_alive():
                self.logger.info("等待指标更新线程结束...")
                self.metrics_thread.join(timeout=5)
            
            self.logger.info("服务已优雅关闭")
            
        except Exception as e:
            self.logger.error(f"关闭服务时发生错误: {e}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='市场数据消费者服务')
    parser.add_argument('--config', default='config.yaml', help='RabbitMQ配置文件路径')
    parser.add_argument('--db-config', default='config/database.yaml', help='数据库配置文件路径')
    parser.add_argument('--metrics-port', type=int, default=8000, help='Prometheus指标端口')
    parser.add_argument('--health-port', type=int, default=8080, help='健康检查端口')
    parser.add_argument('--log-level', default='INFO', 
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='日志级别')
    
    args = parser.parse_args()
    
    # 设置日志级别
    logging.getLogger().setLevel(getattr(logging, args.log_level))
    
    # 创建并启动服务
    service = MarketDataConsumerService(
        config_path=args.config,
        db_config_path=args.db_config,
        metrics_port=args.metrics_port,
        health_port=args.health_port
    )
    
    service.start()


if __name__ == '__main__':
    main()
