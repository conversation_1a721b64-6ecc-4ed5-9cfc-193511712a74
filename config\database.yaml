# MySQL数据库配置文件
database:
  # 主数据库配置
  primary:
    host: "127.0.0.1"
    port: 3306
    username: "root"
    password: "root"
    database: "market_data"
    charset: "utf8mb4"
    
  # 连接池配置
  pool:
    min_connections: 5
    max_connections: 20
    max_idle_time: 300  # 秒
    max_lifetime: 3600  # 秒
    
  # 连接参数
  connection:
    connect_timeout: 10  # 连接超时（秒）
    read_timeout: 30     # 读取超时（秒）
    write_timeout: 30    # 写入超时（秒）
    autocommit: true     # 自动提交
    
  # 重试配置
  retry:
    max_retries: 3
    retry_delay: 1  # 秒
    backoff_factor: 2
    
  # 读写分离配置（可选）
  replica:
    enabled: false
    hosts:
      - host: "localhost"
        port: 3307
        weight: 1
      - host: "localhost"
        port: 3308
        weight: 1

# 数据处理配置
data_processing:
  # 批量插入配置
  batch:
    size: 1000           # 批量大小
    timeout: 5           # 批量超时（秒）
    max_wait_time: 10    # 最大等待时间（秒）
    
  # 数据验证
  validation:
    enabled: true
    strict_mode: false   # 严格模式
    
  # 数据清理
  cleanup:
    enabled: true
    retention_days: 30   # 数据保留天数
    cleanup_interval: 24 # 清理间隔（小时）

# 监控配置
monitoring:
  # 数据库监控
  database:
    enabled: true
    check_interval: 30   # 检查间隔（秒）
    slow_query_threshold: 1.0  # 慢查询阈值（秒）
    
  # 性能监控
  performance:
    enabled: true
    metrics_interval: 60  # 指标收集间隔（秒）
    
# 日志配置
logging:
  # 数据库日志
  database:
    enabled: true
    level: "INFO"
    log_queries: false    # 是否记录SQL查询
    log_slow_queries: true # 是否记录慢查询
    
  # 错误日志
  error:
    enabled: true
    level: "ERROR"
    file: "logs/database_error.log"
