# RabbitMQ配置文件 - Producer服务专用
rabbitmq:
  # RabbitMQ服务器配置
  host: "127.0.0.1"
  port: 5672
  username: "admin"
  password: "admin123"
  virtual_host: "/"
  
  # 队列配置
  queue:
    name: "market_data_queue"
    durable: true  # 队列持久化
    auto_delete: false
    exclusive: false
  
  # 交换机配置
  exchange:
    name: "market_data_exchange"
    type: "direct"
    durable: true
    auto_delete: false
  
  # 路由键
  routing_key: "market.data"
  
  # 连接配置
  connection:
    heartbeat: 600  # 心跳间隔（秒）
    blocked_connection_timeout: 300  # 阻塞连接超时（秒）
    socket_timeout: 10  # socket超时（秒）
    retry_delay: 5  # 重连延迟（秒）
    max_retries: 3  # 最大重试次数

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/producer_rabbitmq.log"

# Producer应用配置
app:
  # 数据处理配置
  batch_size: 50  # 批量发送大小
  flush_interval: 2  # 刷新间隔（秒）
  
  # 数据格式配置
  data_format: "json"  # json 或 msgpack
  compression: false  # 是否压缩数据
  
  # 性能配置
  max_message_size: 1048576  # 最大消息大小（1MB）
  send_timeout: 30  # 发送超时（秒）
