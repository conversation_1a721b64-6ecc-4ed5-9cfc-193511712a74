#!/usr/bin/env python3
"""
配置文件测试脚本
验证RabbitMQ配置文件是否正确
"""

import os
import sys

try:
    import yaml
    YAML_AVAILABLE = True
except ImportError:
    YAML_AVAILABLE = False
    print("⚠️  PyYAML未安装，跳过YAML内容验证")


def test_rabbitmq_config():
    """测试RabbitMQ配置文件"""
    print("🧪 测试RabbitMQ配置文件...")
    
    config_path = "config/rabbitmq_config.yaml"
    
    # 检查文件是否存在
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        if not YAML_AVAILABLE:
            print("⚠️  PyYAML未安装，只检查文件存在性")
            return True

        # 加载配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ 配置文件加载成功: {config_path}")
        
        # 验证必要的配置项
        required_keys = [
            'rabbitmq',
            'logging',
            'app'
        ]
        
        for key in required_keys:
            if key not in config:
                print(f"❌ 缺少配置项: {key}")
                return False
            print(f"✅ 配置项存在: {key}")
        
        # 验证RabbitMQ配置
        rabbitmq_config = config['rabbitmq']
        rabbitmq_required = ['host', 'port', 'username', 'password', 'queue', 'exchange', 'routing_key']
        
        for key in rabbitmq_required:
            if key not in rabbitmq_config:
                print(f"❌ RabbitMQ配置缺少: {key}")
                return False
            print(f"✅ RabbitMQ配置存在: {key}")
        
        # 显示配置摘要
        print("\n📋 配置摘要:")
        print(f"   RabbitMQ主机: {rabbitmq_config['host']}:{rabbitmq_config['port']}")
        print(f"   队列名称: {rabbitmq_config['queue']['name']}")
        print(f"   交换机: {rabbitmq_config['exchange']['name']}")
        print(f"   路由键: {rabbitmq_config['routing_key']}")
        print(f"   数据格式: {config['app']['data_format']}")
        print(f"   批量大小: {config['app']['batch_size']}")
        
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ YAML格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return False


def test_database_config():
    """测试数据库配置文件"""
    print("\n🧪 测试数据库配置文件...")
    
    config_path = "config/database.yaml"
    
    # 检查文件是否存在
    if not os.path.exists(config_path):
        print(f"❌ 配置文件不存在: {config_path}")
        return False
    
    try:
        if not YAML_AVAILABLE:
            print("⚠️  PyYAML未安装，只检查文件存在性")
            return True

        # 加载配置文件
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✅ 配置文件加载成功: {config_path}")
        
        # 验证必要的配置项
        if 'database' not in config:
            print("❌ 缺少database配置项")
            return False
        
        db_config = config['database']
        if 'primary' not in db_config:
            print("❌ 缺少primary数据库配置")
            return False
        
        primary_config = db_config['primary']
        required_keys = ['host', 'port', 'username', 'password', 'database']
        
        for key in required_keys:
            if key not in primary_config:
                print(f"❌ 数据库配置缺少: {key}")
                return False
            print(f"✅ 数据库配置存在: {key}")
        
        # 显示配置摘要
        print("\n📋 数据库配置摘要:")
        print(f"   数据库主机: {primary_config['host']}:{primary_config['port']}")
        print(f"   数据库名: {primary_config['database']}")
        print(f"   用户名: {primary_config['username']}")
        
        return True
        
    except yaml.YAMLError as e:
        print(f"❌ YAML格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 加载配置文件失败: {e}")
        return False


def test_producer_import():
    """测试Producer模块导入"""
    print("\n🧪 测试Producer模块导入...")
    
    try:
        # 测试从producer目录导入
        sys.path.append('producer')
        from rabbitmq_client import get_rabbitmq_client
        
        print("✅ RabbitMQ客户端模块导入成功")
        
        # 测试配置文件路径
        try:
            client = get_rabbitmq_client("../config/rabbitmq_config.yaml")
            print("✅ RabbitMQ客户端初始化成功")
            return True
        except Exception as e:
            print(f"❌ RabbitMQ客户端初始化失败: {e}")
            return False
            
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_directory_structure():
    """测试目录结构"""
    print("\n🧪 测试目录结构...")
    
    required_dirs = [
        'config',
        'producer',
        'consumer',
        'logs',
        'monitoring'
    ]
    
    required_files = [
        'config/rabbitmq_config.yaml',
        'config/database.yaml',
        'config/database_schema.sql',
        'producer/collect_data.py',
        'producer/rabbitmq_client.py',
        'consumer/main.py',
        'docker-compose.yml',
        'requirements.txt'
    ]
    
    # 检查目录
    for dir_name in required_dirs:
        if os.path.exists(dir_name) and os.path.isdir(dir_name):
            print(f"✅ 目录存在: {dir_name}")
        else:
            print(f"❌ 目录不存在: {dir_name}")
            return False
    
    # 检查文件
    for file_name in required_files:
        if os.path.exists(file_name) and os.path.isfile(file_name):
            print(f"✅ 文件存在: {file_name}")
        else:
            print(f"❌ 文件不存在: {file_name}")
            return False
    
    return True


def main():
    """主测试函数"""
    print("🚀 开始配置文件和项目结构测试")
    print("=" * 50)
    
    tests = [
        ("目录结构", test_directory_structure),
        ("RabbitMQ配置", test_rabbitmq_config),
        ("数据库配置", test_database_config),
        ("Producer模块导入", test_producer_import)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！配置文件和项目结构正确")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置文件和项目结构")
        return 1


if __name__ == '__main__':
    exit(main())
