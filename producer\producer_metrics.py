"""
Producer服务Prometheus监控指标模块
专注于监控服务状态和机器状态
"""

import time
import threading
import psutil
import platform
from typing import Dict, Any
from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
import logging


class ProducerMetricsCollector:
    """Producer指标收集器"""
    
    def __init__(self, port: int = 8001):
        """
        初始化Producer指标收集器
        
        Args:
            port: Prometheus指标暴露端口
        """
        self.port = port
        self.logger = logging.getLogger(__name__)
        self.start_time = time.time()
        
        # 定义指标
        self._init_metrics()
        
        # 启动HTTP服务器
        self._start_metrics_server()
        
        # 启动系统指标收集线程
        self._start_system_metrics_thread()
        
    def _init_metrics(self):
        """初始化Prometheus指标"""
        
        # 应用信息
        self.app_info = Info('market_data_producer_info', 'Market data producer application info')
        self.app_info.info({
            'version': '1.0.0',
            'component': 'market_data_producer',
            'platform': platform.platform(),
            'python_version': platform.python_version()
        })
        
        # === 服务状态指标 ===
        self.service_uptime_seconds = Gauge(
            'producer_service_uptime_seconds',
            'Producer service uptime in seconds'
        )
        
        self.service_status = Gauge(
            'producer_service_status',
            'Producer service status (1=running, 0=stopped)'
        )
        
        # QMT连接状态
        self.qmt_connection_status = Gauge(
            'producer_qmt_connection_status',
            'QMT connection status (1=connected, 0=disconnected)'
        )
        
        # RabbitMQ连接状态
        self.rabbitmq_connection_status = Gauge(
            'producer_rabbitmq_connection_status',
            'RabbitMQ connection status (1=connected, 0=disconnected)'
        )
        
        # === 业务指标 ===
        self.messages_sent_total = Counter(
            'producer_messages_sent_total',
            'Total number of messages sent to RabbitMQ',
            ['status']  # success, failed
        )
        
        self.message_send_duration = Histogram(
            'producer_message_send_duration_seconds',
            'Time spent sending messages to RabbitMQ',
            buckets=[0.001, 0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0]
        )
        
        self.data_callbacks_total = Counter(
            'producer_data_callbacks_total',
            'Total number of data callbacks received from QMT'
        )
        
        self.subscribed_stocks_count = Gauge(
            'producer_subscribed_stocks_count',
            'Number of stocks currently subscribed'
        )
        
        # === 系统资源指标 ===
        self.cpu_usage_percent = Gauge(
            'producer_cpu_usage_percent',
            'CPU usage percentage'
        )
        
        self.memory_usage_bytes = Gauge(
            'producer_memory_usage_bytes',
            'Memory usage in bytes'
        )
        
        self.memory_usage_percent = Gauge(
            'producer_memory_usage_percent',
            'Memory usage percentage'
        )
        
        self.disk_usage_percent = Gauge(
            'producer_disk_usage_percent',
            'Disk usage percentage',
            ['device']
        )
        
        self.network_bytes_sent = Counter(
            'producer_network_bytes_sent_total',
            'Total network bytes sent'
        )
        
        self.network_bytes_recv = Counter(
            'producer_network_bytes_recv_total',
            'Total network bytes received'
        )
        
        # === 线程和进程指标 ===
        self.active_threads = Gauge(
            'producer_active_threads',
            'Number of active threads'
        )
        
        self.process_open_files = Gauge(
            'producer_process_open_files',
            'Number of open file descriptors'
        )
        
        # === 错误指标 ===
        self.errors_total = Counter(
            'producer_errors_total',
            'Total number of errors',
            ['error_type']
        )
        
    def _start_metrics_server(self):
        """启动Prometheus指标HTTP服务器"""
        try:
            start_http_server(self.port)
            self.logger.info(f"Producer Prometheus指标服务器已启动，端口: {self.port}")
        except Exception as e:
            self.logger.error(f"启动Producer Prometheus指标服务器失败: {e}")
            raise
    
    def _start_system_metrics_thread(self):
        """启动系统指标收集线程"""
        def collect_system_metrics():
            while True:
                try:
                    self._update_system_metrics()
                    time.sleep(10)  # 每10秒更新一次系统指标
                except Exception as e:
                    self.logger.error(f"更新系统指标时发生错误: {e}")
                    time.sleep(10)
        
        metrics_thread = threading.Thread(target=collect_system_metrics, daemon=True)
        metrics_thread.start()
        self.logger.info("系统指标收集线程已启动")
    
    def _update_system_metrics(self):
        """更新系统指标"""
        try:
            # 服务运行时间
            uptime = time.time() - self.start_time
            self.service_uptime_seconds.set(uptime)
            
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            self.cpu_usage_percent.set(cpu_percent)
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            self.memory_usage_bytes.set(memory.used)
            self.memory_usage_percent.set(memory.percent)
            
            # 磁盘使用情况
            for partition in psutil.disk_partitions():
                try:
                    disk_usage = psutil.disk_usage(partition.mountpoint)
                    usage_percent = (disk_usage.used / disk_usage.total) * 100
                    self.disk_usage_percent.labels(device=partition.device).set(usage_percent)
                except (PermissionError, OSError):
                    # 跳过无法访问的分区
                    continue
            
            # 网络统计
            net_io = psutil.net_io_counters()
            if net_io:
                # 这里使用累计值，Prometheus会自动计算速率
                self.network_bytes_sent._value._value = net_io.bytes_sent
                self.network_bytes_recv._value._value = net_io.bytes_recv
            
            # 线程数
            self.active_threads.set(threading.active_count())
            
            # 打开的文件描述符数量
            try:
                process = psutil.Process()
                self.process_open_files.set(process.num_fds())
            except (AttributeError, psutil.NoSuchProcess):
                # Windows系统或进程不存在时跳过
                pass
            
        except Exception as e:
            self.logger.error(f"更新系统指标失败: {e}")
    
    def set_service_status(self, running: bool):
        """设置服务状态"""
        self.service_status.set(1 if running else 0)
    
    def set_qmt_connection_status(self, connected: bool):
        """设置QMT连接状态"""
        self.qmt_connection_status.set(1 if connected else 0)
    
    def set_rabbitmq_connection_status(self, connected: bool):
        """设置RabbitMQ连接状态"""
        self.rabbitmq_connection_status.set(1 if connected else 0)
    
    def record_message_sent(self, success: bool, duration: float):
        """记录发送的消息"""
        status = 'success' if success else 'failed'
        self.messages_sent_total.labels(status=status).inc()
        if success:
            self.message_send_duration.observe(duration)
    
    def record_data_callback(self):
        """记录数据回调"""
        self.data_callbacks_total.inc()
    
    def set_subscribed_stocks_count(self, count: int):
        """设置订阅股票数量"""
        self.subscribed_stocks_count.set(count)
    
    def record_error(self, error_type: str):
        """记录错误"""
        self.errors_total.labels(error_type=error_type).inc()
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        try:
            return {
                'uptime_seconds': time.time() - self.start_time,
                'cpu_percent': psutil.cpu_percent(),
                'memory_percent': psutil.virtual_memory().percent,
                'active_threads': threading.active_count(),
                'metrics_port': self.port
            }
        except Exception as e:
            self.logger.error(f"获取指标摘要失败: {e}")
            return {'error': str(e)}


# 全局指标收集器实例
_producer_metrics_collector = None


def get_producer_metrics_collector(port: int = 8001) -> ProducerMetricsCollector:
    """获取Producer指标收集器单例"""
    global _producer_metrics_collector
    if _producer_metrics_collector is None:
        _producer_metrics_collector = ProducerMetricsCollector(port)
    return _producer_metrics_collector


def set_service_status(running: bool):
    """设置服务状态"""
    collector = get_producer_metrics_collector()
    collector.set_service_status(running)


def set_qmt_connection_status(connected: bool):
    """设置QMT连接状态"""
    collector = get_producer_metrics_collector()
    collector.set_qmt_connection_status(connected)


def set_rabbitmq_connection_status(connected: bool):
    """设置RabbitMQ连接状态"""
    collector = get_producer_metrics_collector()
    collector.set_rabbitmq_connection_status(connected)


def record_message_sent(success: bool, duration: float):
    """记录发送的消息"""
    collector = get_producer_metrics_collector()
    collector.record_message_sent(success, duration)


def record_data_callback():
    """记录数据回调"""
    collector = get_producer_metrics_collector()
    collector.record_data_callback()


def set_subscribed_stocks_count(count: int):
    """设置订阅股票数量"""
    collector = get_producer_metrics_collector()
    collector.set_subscribed_stocks_count(count)


def record_error(error_type: str):
    """记录错误"""
    collector = get_producer_metrics_collector()
    collector.record_error(error_type)
