-- 股票行情数据库表结构设计
-- 基于报文结构: {'600000.SH': {'time': 1750316403000, 'timetag': '20250619 15:00:03', ...}}

-- 创建数据库
CREATE DATABASE IF NOT EXISTS market_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE market_data;

-- 1. 股票基本信息表
CREATE TABLE IF NOT EXISTS stock_info (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL UNIQUE COMMENT '股票代码',
    name VARCHAR(100) COMMENT '股票名称',
    market VARCHAR(10) COMMENT '市场(SH/SZ)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_symbol (symbol),
    INDEX idx_market (market)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='股票基本信息表';

-- 2. 实时行情主表
CREATE TABLE IF NOT EXISTS market_quotes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL COMMENT '股票代码',
    time BIGINT NOT NULL COMMENT '时间戳(毫秒)',
    timetag VARCHAR(20) NOT NULL COMMENT '时间标签',
    last_price DECIMAL(10,3) COMMENT '最新价',
    open_price DECIMAL(10,3) COMMENT '开盘价',
    high_price DECIMAL(10,3) COMMENT '最高价',
    low_price DECIMAL(10,3) COMMENT '最低价',
    last_close DECIMAL(10,3) COMMENT '昨收价',
    amount BIGINT COMMENT '成交金额',
    volume BIGINT COMMENT '成交量',
    pvolume BIGINT COMMENT '总成交量',
    stock_status INT COMMENT '股票状态',
    open_int INT COMMENT '持仓量',
    settlement_price DECIMAL(10,3) COMMENT '结算价',
    last_settlement_price DECIMAL(10,3) COMMENT '昨结算价',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_symbol_time (symbol, time),
    INDEX idx_time (time),
    INDEX idx_timetag (timetag),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_symbol_time (symbol, time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实时行情主表';

-- 3. 买卖盘口数据表
CREATE TABLE IF NOT EXISTS market_depth (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL COMMENT '股票代码',
    time BIGINT NOT NULL COMMENT '时间戳(毫秒)',
    timetag VARCHAR(20) NOT NULL COMMENT '时间标签',
    
    -- 卖盘价格 (ask_price)
    ask_price_1 DECIMAL(10,3) COMMENT '卖一价',
    ask_price_2 DECIMAL(10,3) COMMENT '卖二价',
    ask_price_3 DECIMAL(10,3) COMMENT '卖三价',
    ask_price_4 DECIMAL(10,3) COMMENT '卖四价',
    ask_price_5 DECIMAL(10,3) COMMENT '卖五价',
    
    -- 买盘价格 (bid_price)
    bid_price_1 DECIMAL(10,3) COMMENT '买一价',
    bid_price_2 DECIMAL(10,3) COMMENT '买二价',
    bid_price_3 DECIMAL(10,3) COMMENT '买三价',
    bid_price_4 DECIMAL(10,3) COMMENT '买四价',
    bid_price_5 DECIMAL(10,3) COMMENT '买五价',
    
    -- 卖盘量 (ask_vol)
    ask_vol_1 BIGINT COMMENT '卖一量',
    ask_vol_2 BIGINT COMMENT '卖二量',
    ask_vol_3 BIGINT COMMENT '卖三量',
    ask_vol_4 BIGINT COMMENT '卖四量',
    ask_vol_5 BIGINT COMMENT '卖五量',
    
    -- 买盘量 (bid_vol)
    bid_vol_1 BIGINT COMMENT '买一量',
    bid_vol_2 BIGINT COMMENT '买二量',
    bid_vol_3 BIGINT COMMENT '买三量',
    bid_vol_4 BIGINT COMMENT '买四量',
    bid_vol_5 BIGINT COMMENT '买五量',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_symbol_time (symbol, time),
    INDEX idx_time (time),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_symbol_time (symbol, time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='买卖盘口数据表';

-- 4. 数据处理日志表
CREATE TABLE IF NOT EXISTS processing_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    message_id VARCHAR(100) COMMENT '消息ID',
    symbol VARCHAR(20) COMMENT '股票代码',
    processing_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '处理时间',
    status ENUM('SUCCESS', 'FAILED', 'RETRY') DEFAULT 'SUCCESS' COMMENT '处理状态',
    error_message TEXT COMMENT '错误信息',
    raw_data JSON COMMENT '原始数据',
    
    INDEX idx_symbol (symbol),
    INDEX idx_status (status),
    INDEX idx_processing_time (processing_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='数据处理日志表';

-- 5. 系统监控表
CREATE TABLE IF NOT EXISTS system_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL COMMENT '指标名称',
    metric_value DECIMAL(15,6) COMMENT '指标值',
    metric_type ENUM('COUNTER', 'GAUGE', 'HISTOGRAM') DEFAULT 'GAUGE' COMMENT '指标类型',
    labels JSON COMMENT '标签',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '时间戳',
    
    INDEX idx_metric_name (metric_name),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统监控指标表';

-- 创建分区表（按日期分区，提高查询性能）
-- 注意：MySQL分区需要根据实际需求调整

-- 插入一些测试数据
INSERT INTO stock_info (symbol, name, market) VALUES 
('600000.SH', '浦发银行', 'SH'),
('000001.SZ', '平安银行', 'SZ'),
('000002.SZ', '万科A', 'SZ')
ON DUPLICATE KEY UPDATE name=VALUES(name), market=VALUES(market);

-- 创建视图：最新行情视图
CREATE OR REPLACE VIEW latest_quotes AS
SELECT 
    mq.*,
    si.name as stock_name,
    si.market
FROM market_quotes mq
INNER JOIN stock_info si ON mq.symbol = si.symbol
INNER JOIN (
    SELECT symbol, MAX(time) as max_time
    FROM market_quotes
    GROUP BY symbol
) latest ON mq.symbol = latest.symbol AND mq.time = latest.max_time;

-- 创建存储过程：清理历史数据
DELIMITER //
CREATE PROCEDURE CleanHistoryData(IN days_to_keep INT)
BEGIN
    DECLARE cutoff_date TIMESTAMP;
    SET cutoff_date = DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    DELETE FROM market_quotes WHERE created_at < cutoff_date;
    DELETE FROM market_depth WHERE created_at < cutoff_date;
    DELETE FROM processing_log WHERE processing_time < cutoff_date;
    DELETE FROM system_metrics WHERE timestamp < cutoff_date;
END //
DELIMITER ;
