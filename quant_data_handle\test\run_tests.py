#!/usr/bin/env python3
"""
测试运行脚本
"""

import subprocess
import sys
import os


def run_tests():
    """运行所有测试"""
    print("开始运行RabbitMQ客户端测试...")
    
    # 确保在正确的目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    try:
        # 运行pytest
        result = subprocess.run([
            sys.executable, '-m', 'pytest', 
            'test_rabbitmq_client.py', 
            '-v',  # 详细输出
            '--tb=short',  # 简短的traceback
            '--cov=rabbitmq_client',  # 代码覆盖率
            '--cov-report=term-missing'  # 显示未覆盖的行
        ], capture_output=True, text=True)
        
        print("STDOUT:")
        print(result.stdout)
        
        if result.stderr:
            print("STDERR:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("\n✅ 所有测试通过!")
        else:
            print(f"\n❌ 测试失败，退出码: {result.returncode}")
            
        return result.returncode
        
    except FileNotFoundError:
        print("❌ pytest未安装，请运行: pip install pytest pytest-cov")
        return 1
    except Exception as e:
        print(f"❌ 运行测试时发生错误: {e}")
        return 1


def install_dependencies():
    """安装依赖"""
    print("安装测试依赖...")
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], check=True)
        print("✅ 依赖安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装依赖失败: {e}")
        return False


if __name__ == '__main__':
    if len(sys.argv) > 1 and sys.argv[1] == '--install':
        if install_dependencies():
            sys.exit(run_tests())
        else:
            sys.exit(1)
    else:
        sys.exit(run_tests())
