# Prometheus配置文件
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 市场数据生产者服务
  - job_name: 'market-data-producer'
    static_configs:
      - targets: ['producer:8001']
    scrape_interval: 10s
    metrics_path: /metrics

  # 市场数据消费者服务
  - job_name: 'market-data-consumer'
    static_configs:
      - targets: ['consumer:8000']
    scrape_interval: 10s
    metrics_path: /metrics
    
  # MySQL监控 (如果安装了mysql_exporter)
  - job_name: 'mysql'
    static_configs:
      - targets: ['mysql-exporter:9104']
    scrape_interval: 30s
    
  # RabbitMQ监控 (如果启用了prometheus插件)
  - job_name: 'rabbitmq'
    static_configs:
      - targets: ['rabbitmq:15692']
    scrape_interval: 30s

# 告警规则 (可选)
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
