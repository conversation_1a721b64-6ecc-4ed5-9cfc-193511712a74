"""
Producer服务监控功能单元测试
"""

import pytest
import time
import threading
from unittest.mock import Mock, patch, MagicMock
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from producer_metrics import ProducerMetricsCollector, get_producer_metrics_collector
from producer_health import ProducerHealthCheckServer, ProducerHealthCheckHandler


class TestProducerMetricsCollector:
    """Producer指标收集器测试类"""
    
    @patch('producer_metrics.start_http_server')
    @patch('producer_metrics.psutil')
    def test_init_metrics_collector(self, mock_psutil, mock_start_server):
        """测试初始化指标收集器"""
        # 模拟psutil
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = Mock(used=1000000, percent=60.0)
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = Mock(bytes_sent=1000, bytes_recv=2000)
        
        collector = ProducerMetricsCollector(port=9001)
        
        assert collector.port == 9001
        mock_start_server.assert_called_once_with(9001)
        
        # 验证指标已初始化
        assert hasattr(collector, 'service_uptime_seconds')
        assert hasattr(collector, 'messages_sent_total')
        assert hasattr(collector, 'cpu_usage_percent')
        assert hasattr(collector, 'memory_usage_bytes')
    
    @patch('producer_metrics.start_http_server')
    @patch('producer_metrics.psutil')
    def test_set_service_status(self, mock_psutil, mock_start_server):
        """测试设置服务状态"""
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = Mock(used=1000000, percent=60.0)
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = Mock(bytes_sent=1000, bytes_recv=2000)
        
        collector = ProducerMetricsCollector(port=9001)
        
        # 测试设置服务状态
        collector.set_service_status(True)
        # 这里只能验证方法被调用，实际值需要集成测试
        
        collector.set_service_status(False)
    
    @patch('producer_metrics.start_http_server')
    @patch('producer_metrics.psutil')
    def test_record_message_sent(self, mock_psutil, mock_start_server):
        """测试记录发送消息"""
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = Mock(used=1000000, percent=60.0)
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = Mock(bytes_sent=1000, bytes_recv=2000)
        
        collector = ProducerMetricsCollector(port=9001)
        
        # 测试记录成功发送
        collector.record_message_sent(True, 0.5)
        
        # 测试记录失败发送
        collector.record_message_sent(False, 1.0)
        
        # 验证指标存在
        assert collector.messages_sent_total is not None
        assert collector.message_send_duration is not None
    
    @patch('producer_metrics.start_http_server')
    @patch('producer_metrics.psutil')
    def test_connection_status_methods(self, mock_psutil, mock_start_server):
        """测试连接状态设置方法"""
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = Mock(used=1000000, percent=60.0)
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = Mock(bytes_sent=1000, bytes_recv=2000)
        
        collector = ProducerMetricsCollector(port=9001)
        
        # 测试QMT连接状态
        collector.set_qmt_connection_status(True)
        collector.set_qmt_connection_status(False)
        
        # 测试RabbitMQ连接状态
        collector.set_rabbitmq_connection_status(True)
        collector.set_rabbitmq_connection_status(False)
        
        # 验证指标存在
        assert collector.qmt_connection_status is not None
        assert collector.rabbitmq_connection_status is not None
    
    @patch('producer_metrics.start_http_server')
    @patch('producer_metrics.psutil')
    def test_business_metrics(self, mock_psutil, mock_start_server):
        """测试业务指标"""
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = Mock(used=1000000, percent=60.0)
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = Mock(bytes_sent=1000, bytes_recv=2000)
        
        collector = ProducerMetricsCollector(port=9001)
        
        # 测试数据回调记录
        collector.record_data_callback()
        
        # 测试设置订阅股票数量
        collector.set_subscribed_stocks_count(30)
        
        # 测试错误记录
        collector.record_error("test_error")
        
        # 验证指标存在
        assert collector.data_callbacks_total is not None
        assert collector.subscribed_stocks_count is not None
        assert collector.errors_total is not None
    
    @patch('producer_metrics.start_http_server')
    @patch('producer_metrics.psutil')
    def test_get_metrics_summary(self, mock_psutil, mock_start_server):
        """测试获取指标摘要"""
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = Mock(used=1000000, percent=60.0)
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = Mock(bytes_sent=1000, bytes_recv=2000)
        
        collector = ProducerMetricsCollector(port=9001)
        
        summary = collector.get_metrics_summary()
        
        assert 'uptime_seconds' in summary
        assert 'cpu_percent' in summary
        assert 'memory_percent' in summary
        assert 'active_threads' in summary
        assert 'metrics_port' in summary
        assert summary['metrics_port'] == 9001


class TestProducerHealthCheckServer:
    """Producer健康检查服务器测试类"""
    
    def test_health_server_init(self):
        """测试健康检查服务器初始化"""
        server = ProducerHealthCheckServer(host='127.0.0.1', port=0)  # 使用端口0让系统分配
        
        assert server.host == '127.0.0.1'
        assert server.port == 0
        assert server.server is None
        assert server.server_thread is None
    
    @patch('producer_health.HTTPServer')
    def test_health_server_start_stop(self, mock_http_server):
        """测试健康检查服务器启动和停止"""
        mock_server = Mock()
        mock_http_server.return_value = mock_server
        
        server = ProducerHealthCheckServer(host='127.0.0.1', port=0)
        
        # 启动服务器
        server.start()
        assert server.server == mock_server
        assert server.server_thread is not None
        
        # 停止服务器
        server.stop()
        mock_server.shutdown.assert_called_once()
        mock_server.server_close.assert_called_once()


class TestProducerHealthCheckHandler:
    """Producer健康检查处理器测试类"""
    
    @patch('producer_health.get_producer_metrics_collector')
    def test_get_system_health(self, mock_get_metrics):
        """测试获取系统健康状态"""
        # 这个测试需要模拟HTTP请求处理，比较复杂
        # 在实际项目中可以使用测试客户端或集成测试
        pass
    
    @patch('producer_health.get_producer_metrics_collector')
    @patch('producer_health.psutil')
    def test_system_info_collection(self, mock_psutil, mock_get_metrics):
        """测试系统信息收集"""
        # 模拟psutil返回值
        mock_psutil.cpu_percent.return_value = 45.0
        mock_psutil.virtual_memory.return_value = Mock(
            percent=65.0,
            available=**********,
            used=**********
        )
        mock_psutil.disk_partitions.return_value = [
            Mock(device='/dev/sda1', mountpoint='/')
        ]
        mock_psutil.disk_usage.return_value = Mock(
            total=**********00,
            used=50000000000
        )
        mock_psutil.net_io_counters.return_value = Mock(
            bytes_sent=1000000,
            bytes_recv=2000000
        )
        
        # 模拟指标收集器
        mock_metrics = Mock()
        mock_metrics.start_time = time.time() - 3600  # 1小时前启动
        mock_get_metrics.return_value = mock_metrics
        
        # 这里可以测试系统信息收集的逻辑
        # 实际测试需要创建处理器实例并调用相关方法


class TestGlobalFunctions:
    """测试全局函数"""
    
    @patch('producer_metrics.ProducerMetricsCollector')
    def test_get_producer_metrics_collector_singleton(self, mock_collector_class):
        """测试指标收集器单例模式"""
        mock_instance = Mock()
        mock_collector_class.return_value = mock_instance
        
        # 重置全局变量
        import producer_metrics
        producer_metrics._producer_metrics_collector = None
        
        collector1 = get_producer_metrics_collector(port=9001)
        collector2 = get_producer_metrics_collector(port=9001)
        
        # 验证单例模式
        assert collector1 is collector2
        mock_collector_class.assert_called_once_with(9001)


class TestIntegration:
    """集成测试"""
    
    @patch('producer_metrics.start_http_server')
    @patch('producer_metrics.psutil')
    @patch('producer_health.HTTPServer')
    def test_producer_monitoring_integration(self, mock_http_server, mock_psutil, mock_start_server):
        """测试Producer监控集成"""
        # 模拟psutil
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value = Mock(used=1000000, percent=60.0)
        mock_psutil.disk_partitions.return_value = []
        mock_psutil.net_io_counters.return_value = Mock(bytes_sent=1000, bytes_recv=2000)
        
        # 模拟HTTP服务器
        mock_server = Mock()
        mock_http_server.return_value = mock_server
        
        # 测试完整的监控初始化流程
        from producer_metrics import get_producer_metrics_collector
        from producer_health import start_producer_health_server
        
        # 重置全局变量
        import producer_metrics
        import producer_health
        producer_metrics._producer_metrics_collector = None
        producer_health._producer_health_server = None
        
        # 初始化监控
        metrics_collector = get_producer_metrics_collector(port=9001)
        health_server = start_producer_health_server(host='127.0.0.1', port=9081)
        
        # 验证初始化
        assert metrics_collector is not None
        assert health_server is not None
        
        # 测试指标记录
        metrics_collector.set_service_status(True)
        metrics_collector.record_message_sent(True, 0.5)
        metrics_collector.record_data_callback()
        
        # 清理
        health_server.stop()


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
