# 市场数据处理系统 Docker Compose 配置
version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: market_data_mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: market_data
      MYSQL_USER: market_user
      MYSQL_PASSWORD: market_pass
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./config/database_schema.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - market_data_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # RabbitMQ消息队列
  rabbitmq:
    image: rabbitmq:3-management
    container_name: market_data_rabbitmq
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest
    ports:
      - "5672:5672"
      - "15672:15672"  # 管理界面
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    networks:
      - market_data_network
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "ping"]
      timeout: 30s
      retries: 3

  # 市场数据生产者服务
  producer:
    build: .
    container_name: market_data_producer
    environment:
      # RabbitMQ配置
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest

      # 服务配置
      METRICS_PORT: 8001
      HEALTH_PORT: 8081
      LOG_LEVEL: INFO
    ports:
      - "8081:8081"  # 健康检查端口
      - "8001:8001"  # Prometheus指标端口
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - market_data_network
    depends_on:
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: ["python", "producer/collect_data.py"]

  # 市场数据消费者服务
  consumer:
    build: .
    container_name: market_data_consumer
    environment:
      # 数据库配置
      MYSQL_HOST: mysql
      MYSQL_PORT: 3306
      MYSQL_USER: market_user
      MYSQL_PASSWORD: market_pass
      MYSQL_DATABASE: market_data

      # RabbitMQ配置
      RABBITMQ_HOST: rabbitmq
      RABBITMQ_PORT: 5672
      RABBITMQ_USER: guest
      RABBITMQ_PASSWORD: guest

      # 服务配置
      METRICS_PORT: 8000
      HEALTH_PORT: 8080
      LOG_LEVEL: INFO
    ports:
      - "8080:8080"  # 健康检查端口
      - "8000:8000"  # Prometheus指标端口
    volumes:
      - ./logs:/app/logs
      - ./config:/app/config
    networks:
      - market_data_network
    depends_on:
      mysql:
        condition: service_healthy
      rabbitmq:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    command: ["python", "consumer/main.py"]

  # Prometheus监控
  prometheus:
    image: prom/prometheus:latest
    container_name: market_data_prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - market_data_network
    depends_on:
      - consumer

  # Grafana可视化
  grafana:
    image: grafana/grafana:latest
    container_name: market_data_grafana
    ports:
      - "3000:3000"
    environment:
      GF_SECURITY_ADMIN_PASSWORD: admin
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - market_data_network
    depends_on:
      - prometheus

volumes:
  mysql_data:
  rabbitmq_data:
  prometheus_data:
  grafana_data:

networks:
  market_data_network:
    driver: bridge
